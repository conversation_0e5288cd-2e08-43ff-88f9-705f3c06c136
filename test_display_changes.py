#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试显示文字修改后的效果
验证所有"临时"、"便携"、"n天期限"等字样已被移除
"""

import sys
from datetime import datetime, timedelta

def test_generator_display():
    """测试生成器的显示文字"""
    print("=" * 50)
    print("测试生成器显示文字")
    print("=" * 50)
    
    try:
        from portable_auth_generator import PortableAuthGenerator
        
        generator = PortableAuthGenerator()
        
        # 生成一个授权码
        start_date = datetime.now().date()
        end_date = start_date + timedelta(days=3)
        user_id = "测试用户"
        
        auth_code = generator.generate_portable_auth_code(start_date, end_date, user_id)
        print(f"生成的授权码: {auth_code}")
        
        # 验证授权码
        valid, auth_data, message = generator.verify_portable_auth_code(auth_code)
        print(f"验证结果: {message}")
        
        # 检查消息中是否包含不应该出现的词汇
        forbidden_words = ["临时", "便携", "天期限", "剩余", "天"]
        found_forbidden = []
        
        for word in forbidden_words:
            if word in message:
                found_forbidden.append(word)
        
        if found_forbidden:
            print(f"⚠️ 发现不应该出现的词汇: {found_forbidden}")
        else:
            print("✅ 生成器显示文字检查通过")
        
        return len(found_forbidden) == 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_verifier_display():
    """测试验证器的显示文字"""
    print("\n" + "=" * 50)
    print("测试验证器显示文字")
    print("=" * 50)
    
    try:
        from portable_auth_verifier import PortableAuthVerifier
        
        verifier = PortableAuthVerifier("测试应用")
        
        # 检查有效授权的消息
        has_valid, message = verifier.has_valid_portable_auth()
        print(f"有效性检查结果: {message}")
        
        # 检查消息中是否包含不应该出现的词汇
        forbidden_words = ["临时", "便携", "天期限", "剩余", "天"]
        found_forbidden = []
        
        for word in forbidden_words:
            if word in message:
                found_forbidden.append(word)
        
        if found_forbidden:
            print(f"⚠️ 发现不应该出现的词汇: {found_forbidden}")
        else:
            print("✅ 验证器显示文字检查通过")
        
        return len(found_forbidden) == 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_auth_dialog_display():
    """测试授权对话框的显示文字"""
    print("\n" + "=" * 50)
    print("测试授权对话框显示文字")
    print("=" * 50)
    
    try:
        # 模拟创建授权对话框并检查文字
        print("模拟授权对话框文字检查:")
        
        # 这些是对话框中应该显示的文字
        dialog_texts = [
            "或者输入授权码:",
            "请输入授权码...",
            "验证授权码",
            "授权验证成功",
            "授权验证通过！"
        ]
        
        print("对话框显示文字:")
        for text in dialog_texts:
            print(f"  - {text}")
        
        # 检查是否包含不应该出现的词汇
        forbidden_words = ["临时", "便携", "天期限", "剩余"]
        found_forbidden = []
        
        for text in dialog_texts:
            for word in forbidden_words:
                if word in text:
                    found_forbidden.append(f"'{word}' in '{text}'")
        
        if found_forbidden:
            print(f"⚠️ 发现不应该出现的词汇: {found_forbidden}")
            return False
        else:
            print("✅ 授权对话框显示文字检查通过")
            return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_integration_display():
    """测试集成系统的显示文字"""
    print("\n" + "=" * 50)
    print("测试集成系统显示文字")
    print("=" * 50)
    
    try:
        from machine_code_verifier import check_authorization_only
        
        # 检查授权（包含便携授权码）
        valid, message, machine_id, md5_value = check_authorization_only("测试应用")
        
        print(f"集成系统授权消息: {message}")
        
        # 检查消息中是否包含不应该出现的词汇
        forbidden_words = ["临时", "便携", "天期限", "剩余"]
        found_forbidden = []
        
        for word in forbidden_words:
            if word in message:
                found_forbidden.append(word)
        
        if found_forbidden:
            print(f"⚠️ 发现不应该出现的词汇: {found_forbidden}")
            return False
        else:
            print("✅ 集成系统显示文字检查通过")
            return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("显示文字修改验证测试")
    print("=" * 60)
    print("检查是否已移除所有'临时'、'便携'、'n天期限'等字样")
    print("=" * 60)
    
    results = []
    
    # 测试各个组件
    results.append(test_generator_display())
    results.append(test_verifier_display())
    results.append(test_auth_dialog_display())
    results.append(test_integration_display())
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有显示文字修改验证通过！")
        print("✅ 用户将看不到任何关于'临时'、'便携'、'期限'的字样")
        print("✅ 授权码功能对用户来说就是正常的授权方式")
        return 0
    else:
        print("⚠️ 部分显示文字仍需调整")
        return 1

if __name__ == "__main__":
    sys.exit(main())
