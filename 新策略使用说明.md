# 新增混剪策略：一个前贴用两次后贴

## 功能概述

在tab_QT中新增了"一个前贴用两次后贴"混剪策略，位置在"一个前贴只用一次后贴"策略的后面。

## 策略逻辑

### 基本原理
- **一个前贴用两个不同的后贴**：每个前贴素材会与两个不同的后贴素材分别混剪，生成2个视频
- **总视频数量**：前贴数量 × 2

### 后贴选择策略

#### 1. 后贴数量充足（后贴数量 ≥ 前贴数量 × 2）
- **策略**：采用不重复的随机后贴选择
- **实现**：使用`random.sample()`确保每个后贴最多被使用一次
- **示例**：3个前贴，6个后贴 → 随机选择6个不重复的后贴进行配对

#### 2. 后贴数量不足（后贴数量 < 前贴数量 × 2）
- **策略**：采用平均分配的随机选择
- **实现**：使用频次计数器确保每个后贴被选择的几率尽可能相同
- **平均原则**：
  - 优先选择使用频次最少的后贴
  - 确保同一个前贴的两个后贴不相同（除非只有1个后贴）
  - 最终使用频次差异控制在0-1之间

## 使用方法

### 1. 准备素材
- **前贴素材**：至少1个视频文件
- **后贴素材**：至少2个视频文件（少于2个时此策略不会显示）

### 2. 选择策略
1. 在tab_QT中导入前贴和后贴素材
2. 点击"初始检查"按钮
3. 在策略选择区域会看到"一个前贴用两次后贴"选项
4. 选择该策略，显示预计生成的视频数量

### 3. 开始混剪
- 点击"开始吧"按钮开始混剪
- 生成的视频文件命名格式：`one_to_two_{前贴序号}_{1或2}.mp4`

## 示例场景

### 场景1：后贴充足
- **素材**：3个前贴，6个后贴
- **结果**：生成6个视频，每个后贴使用1次
- **文件**：`one_to_two_1_1.mp4`, `one_to_two_1_2.mp4`, `one_to_two_2_1.mp4`, `one_to_two_2_2.mp4`, `one_to_two_3_1.mp4`, `one_to_two_3_2.mp4`

### 场景2：后贴不足
- **素材**：3个前贴，4个后贴
- **结果**：生成6个视频，后贴平均分配（每个后贴使用1-2次）
- **分配示例**：
  - 前贴1：后贴A + 后贴B
  - 前贴2：后贴C + 后贴D  
  - 前贴3：后贴A + 后贴C

## 技术特点

### 1. 智能分配算法
- 使用频次计数器跟踪每个后贴的使用次数
- 动态选择使用频次最少的后贴
- 确保分配尽可能平均

### 2. 避免重复配对
- 同一个前贴的两个后贴保证不同（除非只有1个后贴）
- 优化用户体验，避免生成相同内容

### 3. 灵活适应
- 自动适应不同的素材数量比例
- 在后贴不足时仍能合理分配

## 注意事项

1. **最少后贴要求**：至少需要2个后贴素材，否则此策略不会显示
2. **文件命名**：生成的文件按前贴顺序和配对顺序命名
3. **处理时间**：相比"一个前贴只用一次"策略，处理时间会增加一倍
4. **存储空间**：生成的视频数量是前贴数量的2倍，注意磁盘空间

## 与其他策略的对比

| 策略名称 | 生成数量 | 后贴使用 | 适用场景 |
|---------|---------|---------|---------|
| 一个前贴只用一次 | 前贴数量 | 每个后贴最多1次 | 快速生成，节省时间 |
| **一个前贴用两次后贴** | **前贴数量×2** | **平均分配** | **增加视频多样性** |
| 全笛卡尔积 | 前贴×后贴 | 每个后贴多次 | 最大化组合 |

新策略在保持合理处理时间的同时，为每个前贴提供了更多的变化，是一个很好的平衡选择。
