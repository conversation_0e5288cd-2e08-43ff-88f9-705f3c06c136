import os
import subprocess
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading

class VideoResolutionFixer:
    def __init__(self, root):
        self.root = root
        self.root.title("视频分辨率与比特率修复工具")
        self.root.geometry("750x550")
        self.root.resizable(False, False)
        
        # 目标参数
        self.target_width = 1080
        self.target_height = 1920
        self.target_bitrate = 12000  # 默认比特率
        
        # 文件路径
        self.input_path = ""
        self.output_path = ""
        
        # FFmpeg路径
        self.ffmpeg_path = self.normalize_path(
            "D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"
        )
        
        # 创建UI
        self.create_widgets()
        self.check_ffmpeg_availability()

    def normalize_path(self, path):
        """标准化路径为正斜杠格式"""
        path = path.replace("\\", "/").strip('"')
        if len(path) >= 2 and path[1] == ':' and (len(path) < 3 or path[2] != '/'):
            path = path[:2] + '/' + path[2:] if len(path) > 2 else path
        return path

    def create_widgets(self):
        # 分辨率设置
        res_frame = ttk.LabelFrame(self.root, text="目标分辨率", padding=10)
        res_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(res_frame, text="宽度:").grid(row=0, column=0, padx=5, pady=5)
        self.width_entry = ttk.Entry(res_frame, width=10)
        self.width_entry.grid(row=0, column=1, padx=5, pady=5)
        self.width_entry.insert(0, str(self.target_width))
        
        ttk.Label(res_frame, text="高度:").grid(row=0, column=2, padx=5, pady=5)
        self.height_entry = ttk.Entry(res_frame, width=10)
        self.height_entry.grid(row=0, column=3, padx=5, pady=5)
        self.height_entry.insert(0, str(self.target_height))
        
        # 比特率设置
        bitrate_frame = ttk.LabelFrame(self.root, text="目标视频比特率（kbps）", padding=10)
        bitrate_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.bitrate_entry = ttk.Entry(bitrate_frame, width=15)
        self.bitrate_entry.pack(side=tk.LEFT, padx=5, pady=5)
        self.bitrate_entry.insert(0, str(self.target_bitrate))
        ttk.Label(bitrate_frame, text="建议值：原视频比特率左右").pack(side=tk.LEFT, padx=5)
        
        # 文件选择
        file_frame = ttk.LabelFrame(self.root, text="文件选择", padding=10)
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(file_frame, text="选中文件:").pack(side=tk.LEFT, padx=5)
        self.file_label = ttk.Label(file_frame, text="未选择", foreground="gray")
        self.file_label.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        # 按钮
        btn_frame = ttk.Frame(self.root, padding=10)
        btn_frame.pack()
        
        self.select_btn = ttk.Button(btn_frame, text="选择视频", command=self.select_file)
        self.select_btn.pack(side=tk.LEFT, padx=10)
        
        self.info_btn = ttk.Button(btn_frame, text="查看原视频信息", command=self.show_video_info)
        self.info_btn.pack(side=tk.LEFT, padx=10)
        
        self.process_btn = ttk.Button(btn_frame, text="开始修复", command=self.start_processing)
        self.process_btn.pack(side=tk.LEFT, padx=10)
        
        # 日志
        log_frame = ttk.LabelFrame(self.root, text="处理日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD)
        self.log_text.pack(padx=5, pady=5, fill=tk.BOTH, expand=True, side=tk.LEFT)
        
        scrollbar = ttk.Scrollbar(log_frame, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set, state=tk.DISABLED)
        
        # 进度
        progress_frame = ttk.Frame(self.root, padding=10)
        progress_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(progress_frame, text="进度:").pack(anchor=tk.W, padx=5)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, length=650)
        self.progress_bar.pack(padx=5, pady=5, fill=tk.X)
        
        self.status_label = ttk.Label(progress_frame, text="就绪", foreground="blue")
        self.status_label.pack(anchor=tk.W, padx=5)

    def log(self, message):
        """添加日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def check_ffmpeg_availability(self):
        """检查FFmpeg"""
        try:
            if not os.path.exists(self.ffmpeg_path):
                self.log(f"FFmpeg未找到：{self.ffmpeg_path}")
                self.status_label.config(text="FFmpeg路径错误", foreground="red")
                return
            
            result = subprocess.run(
                [self.ffmpeg_path, "-version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True
            )
            
            output = result.stdout.decode('utf-8', errors='replace')
            if result.returncode == 0:
                self.log("FFmpeg就绪：" + output.splitlines()[0])
                self.status_label.config(text="FFmpeg就绪", foreground="green")
            else:
                self.log(f"FFmpeg错误：{result.stderr.decode('utf-8')}")
                self.status_label.config(text="FFmpeg异常", foreground="red")
                
        except Exception as e:
            self.log(f"FFmpeg检查失败：{str(e)}")

    def select_file(self):
        """选择文件"""
        file_path = filedialog.askopenfilename(filetypes=[("视频文件", "*.mp4 *.mov *.avi *.mkv")])
        if file_path:
            self.input_path = self.normalize_path(file_path)
            self.file_label.config(text=os.path.basename(file_path), foreground="black")
            self.log(f"选中文件：{self.input_path}")
            # 自动读取原视频比特率并建议
            self.get_original_bitrate()

    def get_original_bitrate(self):
        """获取原视频比特率并显示建议值"""
        try:
            cmd = f'"{self.ffmpeg_path}" -i "{self.input_path}"'
            result = subprocess.run(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True
            )
            output = result.stdout.decode('utf-8', errors='replace')
            for line in output.splitlines():
                if "bitrate:" in line and "Video" not in line:  # 提取总比特率
                    bitrate_str = line.split("bitrate:")[1].strip().split(" ")[0]
                    if bitrate_str.isdigit():
                        self.target_bitrate = int(bitrate_str)
                        self.bitrate_entry.delete(0, tk.END)
                        self.bitrate_entry.insert(0, str(self.target_bitrate))
                        self.log(f"检测到原视频比特率：{self.target_bitrate}kbps，已自动设置为目标值")
                        break
        except Exception as e:
            self.log(f"获取原比特率失败：{str(e)}（可手动输入）")

    def show_video_info(self):
        """显示原视频信息"""
        if not self.input_path:
            messagebox.showwarning("提示", "请先选择视频")
            return
        try:
            cmd = f'"{self.ffmpeg_path}" -i "{self.input_path}"'
            result = subprocess.run(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True
            )
            output = result.stdout.decode('utf-8', errors='replace')
            self.log("\n原视频信息：")
            for line in output.splitlines():
                if "Stream #0:0" in line or "Duration" in line or "bitrate" in line:
                    self.log(line.strip())
        except Exception as e:
            self.log(f"获取信息失败：{str(e)}")

    def start_processing(self):
        """开始处理"""
        if not self.input_path:
            messagebox.showwarning("提示", "请选择视频")
            return
        
        # 验证参数
        try:
            self.target_width = int(self.width_entry.get())
            self.target_height = int(self.height_entry.get())
            self.target_bitrate = int(self.bitrate_entry.get())
            if self.target_bitrate < 500:
                raise ValueError("比特率过低（建议≥500kbps）")
        except ValueError as e:
            messagebox.showerror("参数错误", str(e))
            return
        
        # 输出路径
        input_dir = os.path.dirname(self.input_path)
        input_name = os.path.basename(self.input_path)
        name, ext = os.path.splitext(input_name)
        self.output_path = f"{input_dir}/{name}_rem{ext}".replace("\\", "/")
        temp_output = f"{input_dir}/{name}_temp{ext}".replace("\\", "/")
        
        # 检查权限
        if not os.access(input_dir, os.W_OK):
            messagebox.showerror("权限错误", f"无法写入：{input_dir}")
            return
        
        # 锁定按钮
        self.select_btn.config(state=tk.DISABLED)
        self.info_btn.config(state=tk.DISABLED)
        self.process_btn.config(state=tk.DISABLED)
        self.status_label.config(text="处理中...", foreground="orange")
        self.progress_var.set(0)
        self.log(f"开始修复：分辨率{self.target_width}x{self.target_height}，比特率{self.target_bitrate}kbps")
        self.log(f"输出路径：{self.output_path}")
        
        # 启动线程
        threading.Thread(target=self.process_video, args=(temp_output,), daemon=True).start()

    def process_video(self, temp_output):
        """处理视频（核心）"""
        try:
            # 编码命令：使用目标比特率
            cmd = (
                f'"{self.ffmpeg_path}" '
                f'-i "{self.input_path}" '
                f'-vf "scale={self.target_width}:{self.target_height}:force_original_aspect_ratio=disable" '
                f'-c:v libx264 -b:v {self.target_bitrate}k -minrate {self.target_bitrate*0.9}k -maxrate {self.target_bitrate*1.1}k '
                f'-bufsize {self.target_bitrate*2}k -preset medium -pix_fmt yuv420p -qcomp 0.3 '  # 优化比特率稳定性
                f'-c:a copy -y "{temp_output}"'
            )
            self.log(f"执行命令：{cmd}")
            
            # 执行编码
            process = subprocess.Popen(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True, bufsize=1
            )
            
            # 实时日志
            duration = self.get_duration()
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                line_str = line.decode('utf-8', errors='replace').strip()
                if line_str:
                    self.log(line_str)
                # 更新进度
                if "time=" in line_str and duration:
                    try:
                        time_str = line_str.split("time=")[1].split(" ")[0]
                        h, m, s = time_str.split(":")
                        current = int(h)*3600 + int(m)*60 + float(s)
                        self.progress_var.set(min(90, (current/duration)*90))
                    except:
                        pass
            
            process.wait()
            if process.returncode != 0:
                raise Exception(f"编码失败，返回码：{process.returncode}")
            
            # 修正元数据
            final_cmd = f'"{self.ffmpeg_path}" -i "{temp_output}" -c copy -y "{self.output_path}"'
            self.log(f"修正元数据：{final_cmd}")
            final_process = subprocess.run(
                final_cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True
            )
            for line in final_process.stdout.decode('utf-8', errors='replace').splitlines():
                if line.strip():
                    self.log(line.strip())
            
            if final_process.returncode != 0:
                raise Exception(f"元数据修正失败，返回码：{final_process.returncode}")
            
            # 清理临时文件
            if os.path.exists(temp_output):
                os.remove(temp_output)
            
            # 验证结果（仅验证分辨率，不验证比特率）
            self.verify_result()
            
            # 完成
            self.progress_var.set(100)
            self.status_label.config(text="修复完成", foreground="green")
            self.log(f"修复成功！文件：{self.output_path}")
            messagebox.showinfo("成功", f"修复完成\n{self.output_path}")
        
        except Exception as e:
            # 仅在编码/元数据步骤失败时显示错误弹窗，比特率问题不弹窗
            error_msg = f"修复失败：{str(e)}"
            self.log(error_msg)
            self.status_label.config(text=error_msg, foreground="red")
            messagebox.showerror("失败", error_msg)
        
        finally:
            # 解锁按钮
            self.select_btn.config(state=tk.NORMAL)
            self.info_btn.config(state=tk.NORMAL)
            self.process_btn.config(state=tk.NORMAL)

    def get_duration(self):
        """获取视频时长（用于进度计算）"""
        try:
            cmd = f'"{self.ffmpeg_path}" -i "{self.input_path}" -show_entries format=duration -v quiet -of csv=p=0'
            result = subprocess.run(cmd, stdout=subprocess.PIPE, shell=True)
            return float(result.stdout.decode('utf-8').strip())
        except:
            return None

    def verify_result(self):
        """验证修复结果（仅检查分辨率，不检查比特率）"""
        try:
            if not os.path.exists(self.output_path) or os.path.getsize(self.output_path) == 0:
                raise Exception("修复后的文件不存在或为空")
            
            cmd = f'"{self.ffmpeg_path}" -i "{self.output_path}"'
            result = subprocess.run(
                cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True
            )
            output = result.stdout.decode('utf-8', errors='replace')
            self.log("\n修复后信息：")
            res_ok = False
            actual_bitrate = 0
            
            # 提取分辨率和实际比特率（仅日志显示，不验证）
            for line in output.splitlines():
                line = line.strip()
                if "Stream #0:0" in line:
                    self.log(line)
                    if f"{self.target_width}x{self.target_height}" in line:
                        res_ok = True
                if "bitrate:" in line and "Video" not in line:
                    self.log(line)
                    try:
                        actual_bitrate = int(line.split("bitrate:")[1].strip().split(" ")[0])
                    except:
                        pass
            
            # 仅验证分辨率
            if not res_ok:
                raise Exception(f"分辨率未达到目标：{self.target_width}x{self.target_height}")
            
            # 比特率仅日志提示，不影响结果
            if actual_bitrate > 0:
                self.log(f"实际比特率：{actual_bitrate}kbps（目标：{self.target_bitrate}kbps）")
            self.log("分辨率验证通过！")
            
        except Exception as e:
            raise Exception(f"验证失败：{str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = VideoResolutionFixer(root)
    root.mainloop()