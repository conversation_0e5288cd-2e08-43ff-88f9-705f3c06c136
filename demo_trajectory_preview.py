#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示不同视频的动态水印轨迹预览
"""

import sys
import os
import random
import hashlib
import math

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def generate_trajectory_preview(video_name, duration=10):
    """为指定视频生成轨迹预览"""
    # 使用视频文件名作为随机种子
    seed = int(hashlib.md5(video_name.encode()).hexdigest()[:8], 16)
    random.seed(seed)
    
    # 生成随机参数（与实际代码一致）
    logo_width = 220
    max_speed_x = random.randint(400, 800)
    max_speed_y = random.randint(400, 800)
    
    freq1_x = round(random.uniform(0.1, 1.0), 2)
    freq2_x = round(random.uniform(0.1, 1.0), 2)
    freq3_x = round(random.uniform(0.1, 1.0), 2)
    freq1_y = round(random.uniform(0.1, 1.0), 2)
    freq2_y = round(random.uniform(0.1, 1.0), 2)
    freq3_y = round(random.uniform(0.1, 1.0), 2)
    
    phase1_x = round(random.uniform(0, 6.28), 2)
    phase2_x = round(random.uniform(0, 6.28), 2)
    phase3_x = round(random.uniform(0, 6.28), 2)
    phase1_y = round(random.uniform(0, 6.28), 2)
    phase2_y = round(random.uniform(0, 6.28), 2)
    phase3_y = round(random.uniform(0, 6.28), 2)
    
    amp1_x = round(random.uniform(0.3, 0.7), 2)
    amp2_x = round(random.uniform(0.2, 0.5), 2)
    amp3_x = round(random.uniform(0.1, 0.3), 2)
    amp1_y = round(random.uniform(0.3, 0.7), 2)
    amp2_y = round(random.uniform(0.2, 0.5), 2)
    amp3_y = round(random.uniform(0.1, 0.3), 2)
    
    # 运动模式
    motion_patterns = [
        ("正弦+余弦组合", 
         lambda t: (540 + max_speed_x*amp1_x*math.sin(freq1_x*t + phase1_x) + max_speed_x*amp2_x*math.cos(freq2_x*t + phase2_x) + max_speed_x*amp3_x*math.sin(freq3_x*t + phase3_x),
                   960 + max_speed_y*amp1_y*math.cos(freq1_y*t + phase1_y) + max_speed_y*amp2_y*math.sin(freq2_y*t + phase2_y) + max_speed_y*amp3_y*math.cos(freq3_y*t + phase3_y))),
        
        ("椭圆轨迹",
         lambda t: (540 + max_speed_x*amp1_x*math.sin(freq1_x*t + phase1_x),
                   960 + max_speed_y*amp1_y*math.cos(freq1_y*t + phase1_y))),
        
        ("8字形轨迹",
         lambda t: (540 + max_speed_x*amp1_x*math.sin(freq1_x*t + phase1_x),
                   960 + max_speed_y*amp1_y*math.sin(freq1_y*2*t + phase1_y))),
        
        ("复杂波形",
         lambda t: (540 + max_speed_x*amp1_x*math.sin(freq1_x*t + phase1_x) + max_speed_x*amp2_x*math.sin(freq2_x*3*t + phase2_x),
                   960 + max_speed_y*amp1_y*math.cos(freq1_y*t + phase1_y) + max_speed_y*amp2_y*math.cos(freq2_y*2*t + phase2_y)))
    ]
    
    # 随机选择运动模式
    pattern_name, pattern_func = random.choice(motion_patterns)
    
    # 生成轨迹点
    points = []
    for i in range(duration * 10):  # 每秒10个点
        t = i / 10.0
        x, y = pattern_func(t)
        # 确保在屏幕范围内
        x = max(0, min(1080 - logo_width, x))
        y = max(0, min(1920 - logo_width, y))
        points.append((x, y))
    
    return {
        'video_name': video_name,
        'seed': seed % 10000,
        'pattern': pattern_name,
        'max_speed_x': max_speed_x,
        'max_speed_y': max_speed_y,
        'points': points,
        'params': {
            'freq1_x': freq1_x, 'freq1_y': freq1_y,
            'amp1_x': amp1_x, 'amp1_y': amp1_y,
            'phase1_x': phase1_x, 'phase1_y': phase1_y
        }
    }

def print_trajectory_info(traj_info):
    """打印轨迹信息"""
    print(f"\n📹 视频: {traj_info['video_name']}")
    print(f"🎯 种子: {traj_info['seed']}")
    print(f"🎨 运动模式: {traj_info['pattern']}")
    print(f"📏 X轴范围: {traj_info['max_speed_x']}px")
    print(f"📏 Y轴范围: {traj_info['max_speed_y']}px")
    print(f"⚙️  主要参数: 频率X={traj_info['params']['freq1_x']}, 频率Y={traj_info['params']['freq1_y']}")
    print(f"⚙️  幅度参数: X={traj_info['params']['amp1_x']}, Y={traj_info['params']['amp1_y']}")
    
    # 分析轨迹特征
    points = traj_info['points']
    x_coords = [p[0] for p in points]
    y_coords = [p[1] for p in points]
    
    x_range = max(x_coords) - min(x_coords)
    y_range = max(y_coords) - min(y_coords)
    
    print(f"📊 实际移动范围: X={x_range:.0f}px, Y={y_range:.0f}px")
    print(f"📍 起始位置: ({points[0][0]:.0f}, {points[0][1]:.0f})")
    print(f"📍 中间位置: ({points[len(points)//2][0]:.0f}, {points[len(points)//2][1]:.0f})")
    print(f"📍 结束位置: ({points[-1][0]:.0f}, {points[-1][1]:.0f})")

def create_ascii_preview(traj_info, width=60, height=30):
    """创建ASCII轨迹预览"""
    points = traj_info['points']
    
    # 创建画布
    canvas = [[' ' for _ in range(width)] for _ in range(height)]
    
    # 映射坐标到画布
    x_coords = [p[0] for p in points]
    y_coords = [p[1] for p in points]
    
    x_min, x_max = min(x_coords), max(x_coords)
    y_min, y_max = min(y_coords), max(y_coords)
    
    # 避免除零
    x_range = max(x_max - x_min, 1)
    y_range = max(y_max - y_min, 1)
    
    # 绘制轨迹
    for i, (x, y) in enumerate(points[::5]):  # 每5个点取一个
        canvas_x = int((x - x_min) / x_range * (width - 1))
        canvas_y = int((y - y_min) / y_range * (height - 1))
        
        if 0 <= canvas_x < width and 0 <= canvas_y < height:
            if i == 0:
                canvas[canvas_y][canvas_x] = 'S'  # 起点
            elif i == len(points[::5]) - 1:
                canvas[canvas_y][canvas_x] = 'E'  # 终点
            else:
                canvas[canvas_y][canvas_x] = '*'  # 轨迹点
    
    # 打印画布
    print(f"\n🎨 轨迹预览 (S=起点, E=终点, *=轨迹):")
    print("+" + "-" * width + "+")
    for row in canvas:
        print("|" + "".join(row) + "|")
    print("+" + "-" * width + "+")

def main():
    """主演示函数"""
    print("=== 动态水印轨迹预览演示 ===")
    print("这个演示展示了不同视频文件会产生什么样的动态水印轨迹")
    
    # 测试视频列表
    test_videos = [
        "video1.mp4",
        "video2.mp4", 
        "sample_video.avi",
        "test_watermark.mov",
        "动态演示.mkv"
    ]
    
    print(f"\n将为 {len(test_videos)} 个视频生成不同的轨迹...")
    
    for i, video in enumerate(test_videos, 1):
        print(f"\n{'='*60}")
        print(f"第 {i} 个视频轨迹")
        print('='*60)
        
        # 生成轨迹
        traj_info = generate_trajectory_preview(video)
        
        # 打印详细信息
        print_trajectory_info(traj_info)
        
        # 创建ASCII预览
        create_ascii_preview(traj_info)
        
        if i < len(test_videos):
            input("\n按回车键查看下一个视频的轨迹...")
    
    print(f"\n{'='*60}")
    print("演示完成！")
    print("🎉 每个视频都有独特的动态水印轨迹：")
    print("   • 不同的运动模式（正弦波、椭圆、8字形、复杂波形）")
    print("   • 不同的移动速度和范围")
    print("   • 不同的频率和相位参数")
    print("   • 相同视频始终产生相同轨迹（一致性）")
    print("\n💡 现在你的动态水印不再是固定轨迹了！")

if __name__ == "__main__":
    main()
