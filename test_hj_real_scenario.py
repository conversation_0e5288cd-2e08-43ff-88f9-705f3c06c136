#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
tab_HJ真实场景测试脚本 - 模拟实际的中文路径和复杂滤镜
"""

import subprocess
import os
import tempfile
from pathlib import Path

# Windows下隐藏控制台窗口
CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

# 配置参数 - 与tab_HJ相同
VIDEO_BITRATE = "15000k"
GPU_ENABLED = True
CPU_PRESET = "slow"
GPU_PRESET = "p2"

def create_test_video_with_chinese_path(output_path, duration=5, width=1920, height=1080):
    """创建测试视频（中文路径）"""
    ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    
    cmd = [
        str(ffmpeg_path),
        "-f", "lavfi",
        "-i", f"testsrc2=duration={duration}:size={width}x{height}:rate=30",
        "-f", "lavfi", 
        "-i", f"sine=frequency=1000:duration={duration}",
        "-c:v", "libx264",
        "-c:a", "aac",
        "-t", str(duration),
        "-y",
        str(output_path)
    ]
    
    print(f"🎬 创建测试视频: {output_path}")
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        creationflags=CREATE_NO_WINDOW
    )
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"✅ 测试视频创建成功: {os.path.getsize(output_path)} bytes")
        return True
    else:
        print(f"❌ 测试视频创建失败: {result.stderr[:200]}")
        return False

def test_complex_transition_filter(video1, video2, output_path):
    """测试复杂的转场滤镜（模拟tab_HJ的实际滤镜）"""
    print(f"\n🔍 测试复杂转场滤镜...")
    
    ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    
    # 模拟tab_HJ的复杂滤镜（包含缩放、填充、转场、响度统一）
    target_width, target_height = 1920, 1080
    transition_type = "fade"
    safe_duration = 1.0
    offset = 4.0
    
    # 构建复杂的滤镜链（与tab_HJ相同）
    filter_complex = f"[0:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v0];"
    filter_complex += f"[1:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v1];"
    filter_complex += f"[v0][v1]xfade=transition='{transition_type}':duration={safe_duration:.3f}:offset={offset:.3f}[outv];"
    
    # 添加响度统一（tab_HJ的特色功能）
    filter_complex += f"[0:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[a0_norm];"
    filter_complex += f"[1:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[a1_norm];"
    filter_complex += f"[a0_norm]afade=t=out:st={offset:.3f}:d={safe_duration:.3f}[a0_fade];"
    filter_complex += f"[a1_norm]adelay={int(offset*1000)}|{int(offset*1000)}[a1_delayed];"
    filter_complex += f"[a1_delayed]afade=t=in:st={offset:.3f}:d={safe_duration:.3f}[a1_fade];"
    filter_complex += f"[a0_fade][a1_fade]amix=inputs=2:duration=longest:normalize=0[outa]"
    
    cmd = [
        str(ffmpeg_path),
        "-i", str(video1),
        "-i", str(video2),
        "-filter_complex", filter_complex,
        "-map", "[outv]",
        "-map", "[outa]",
        "-c:v", "libx264",
        "-c:a", "aac",
        "-b:v", VIDEO_BITRATE,
        "-maxrate", "18000k",
        "-bufsize", "36000k",
        "-b:a", "256k",
        "-ar", "48000",
        "-preset", CPU_PRESET,
        "-y",
        str(output_path)
    ]
    
    print(f"🔧 复杂滤镜命令: {' '.join(cmd[:8])}...")
    print(f"🎬 滤镜长度: {len(filter_complex)} 字符")
    print(f"🎬 滤镜内容: {filter_complex[:100]}...")
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        timeout=180,
        creationflags=CREATE_NO_WINDOW
    )
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"✅ 复杂滤镜成功: {os.path.getsize(output_path)} bytes")
        return True
    else:
        print(f"❌ 复杂滤镜失败 (返回码: {result.returncode})")
        print(f"错误详情: {result.stderr[:500]}")
        return False

def test_gpu_complex_filter(video1, video2, output_path):
    """测试GPU编码的复杂滤镜"""
    print(f"\n🔍 测试GPU复杂滤镜...")
    
    ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    
    # 与CPU版本相同的复杂滤镜
    target_width, target_height = 1920, 1080
    transition_type = "fade"
    safe_duration = 1.0
    offset = 4.0
    
    filter_complex = f"[0:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v0];"
    filter_complex += f"[1:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v1];"
    filter_complex += f"[v0][v1]xfade=transition='{transition_type}':duration={safe_duration:.3f}:offset={offset:.3f}[outv];"
    filter_complex += f"[0:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[a0_norm];"
    filter_complex += f"[1:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[a1_norm];"
    filter_complex += f"[a0_norm]afade=t=out:st={offset:.3f}:d={safe_duration:.3f}[a0_fade];"
    filter_complex += f"[a1_norm]adelay={int(offset*1000)}|{int(offset*1000)}[a1_delayed];"
    filter_complex += f"[a1_delayed]afade=t=in:st={offset:.3f}:d={safe_duration:.3f}[a1_fade];"
    filter_complex += f"[a0_fade][a1_fade]amix=inputs=2:duration=longest:normalize=0[outa]"
    
    cmd = [
        str(ffmpeg_path),
        "-hwaccel", "cuda",
        "-i", str(video1),
        "-i", str(video2),
        "-filter_complex", filter_complex,
        "-map", "[outv]",
        "-map", "[outa]",
        "-c:v", "h264_nvenc",
        "-c:a", "aac",
        "-b:v", VIDEO_BITRATE,
        "-maxrate", "18000k",
        "-bufsize", "36000k",
        "-b:a", "256k",
        "-ar", "48000",
        "-preset", GPU_PRESET,
        "-bf", "0",
        "-y",
        str(output_path)
    ]
    
    print(f"🔧 GPU复杂命令: {' '.join(cmd[:10])}...")
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        timeout=180,
        creationflags=CREATE_NO_WINDOW
    )
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"✅ GPU复杂滤镜成功: {os.path.getsize(output_path)} bytes")
        return True
    else:
        print(f"❌ GPU复杂滤镜失败 (返回码: {result.returncode})")
        print(f"错误详情: {result.stderr[:500]}")
        return False

def test_chinese_path_handling():
    """测试中文路径处理"""
    print(f"\n🔍 测试中文路径处理...")
    
    # 创建包含中文的临时目录
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        chinese_dir = Path(temp_dir) / "测试文件夹"
        chinese_dir.mkdir(exist_ok=True)
        
        video1 = chinese_dir / "测试视频1.mp4"
        video2 = chinese_dir / "测试视频2.mp4"
        output = chinese_dir / "混剪结果.mp4"
        
        print(f"🎬 中文路径: {chinese_dir}")
        
        # 创建测试视频
        if not create_test_video_with_chinese_path(video1):
            return False
        if not create_test_video_with_chinese_path(video2):
            return False
        
        # 测试简单拼接
        return test_simple_concat_chinese(video1, video2, output)

def test_simple_concat_chinese(video1, video2, output_path):
    """测试中文路径的简单拼接"""
    ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    
    # 创建concat文件
    concat_file = Path(output_path).parent / "concat_中文.txt"
    with open(concat_file, 'w', encoding='utf-8') as f:
        # 使用绝对路径并确保路径格式正确
        video1_abs = str(video1.absolute()).replace('\\', '/')
        video2_abs = str(video2.absolute()).replace('\\', '/')
        f.write(f"file '{video1_abs}'\n")
        f.write(f"file '{video2_abs}'\n")
    
    cmd = [
        str(ffmpeg_path),
        "-f", "concat",
        "-safe", "0",
        "-i", str(concat_file),
        "-c", "copy",
        "-y",
        str(output_path)
    ]
    
    print(f"🔧 中文路径拼接: {' '.join(cmd[:8])}...")
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        timeout=60,
        creationflags=CREATE_NO_WINDOW
    )
    
    # 清理临时文件
    try:
        concat_file.unlink()
    except:
        pass
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"✅ 中文路径拼接成功: {os.path.getsize(output_path)} bytes")
        return True
    else:
        print(f"❌ 中文路径拼接失败: {result.stderr[:300]}")
        return False

def main():
    """主测试函数"""
    print("🎬 tab_HJ真实场景测试")
    print("=" * 50)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试视频
        video1 = temp_path / "test_video1.mp4"
        video2 = temp_path / "test_video2.mp4"
        
        print("🎬 创建测试视频...")
        if not create_test_video_with_chinese_path(video1, duration=5):
            print("❌ 无法创建测试视频1")
            return
        
        if not create_test_video_with_chinese_path(video2, duration=5):
            print("❌ 无法创建测试视频2")
            return
        
        # 测试1: 复杂转场滤镜（CPU）
        output1 = temp_path / "output_complex_cpu.mp4"
        success1 = test_complex_transition_filter(video1, video2, output1)
        
        # 测试2: 复杂转场滤镜（GPU）
        output2 = temp_path / "output_complex_gpu.mp4"
        success2 = test_gpu_complex_filter(video1, video2, output2)
        
        # 测试3: 中文路径处理
        success3 = test_chinese_path_handling()
        
        # 汇总结果
        print("\n" + "=" * 50)
        print("📊 真实场景测试结果:")
        print(f"  复杂滤镜(CPU): {'✅ 成功' if success1 else '❌ 失败'}")
        print(f"  复杂滤镜(GPU): {'✅ 成功' if success2 else '❌ 失败'}")
        print(f"  中文路径处理: {'✅ 成功' if success3 else '❌ 失败'}")
        
        if not success1 and not success2:
            print("\n❌ 复杂滤镜测试失败，可能是响度统一或转场滤镜问题")
        elif success1 and not success2:
            print("\n🔍 CPU成功但GPU失败，可能是GPU编码器与复杂滤镜的兼容性问题")
        elif not success3:
            print("\n🔍 中文路径处理失败，可能是编码或路径格式问题")
        else:
            print("\n✅ 所有真实场景测试通过！")

if __name__ == "__main__":
    main()
