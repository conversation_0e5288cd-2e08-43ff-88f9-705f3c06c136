#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包配置文件
用于配置PyInstaller打包参数
"""

import os
import sys
from pathlib import Path

# 项目基本信息
APP_NAME = "MFChen视频混剪工具"
APP_VERSION = "2.3"
APP_DESCRIPTION = "专业的视频混剪和水印处理工具"
APP_AUTHOR = "MFChen"

# 主要文件
MAIN_SCRIPT = "main.py"
UI_FILE = "ui_main_window.py"

# 需要包含的数据文件和文件夹
DATA_FILES = [
    # photo文件夹及其所有内容
    ('photo', 'photo'),
    
    # 其他可能需要的文件
    # ('config', 'config'),  # 如果有配置文件夹
]

# 需要包含的Python模块
HIDDEN_IMPORTS = [
    'PySide6.QtCore',
    'PySide6.QtWidgets', 
    'PySide6.QtGui',
    'resource_manager',
    'utils',
    'video_mixer',
    'machine_code_verifier',
    'portable_auth_verifier',
    'startup_optimizer',
]

# 需要排除的模块（减小打包体积）
EXCLUDES = [
    'tkinter',
    'matplotlib',
    'numpy',
    'scipy',
    'pandas',
    'PIL',
    'cv2',
    'torch',
    'tensorflow',
]

# PyInstaller命令行参数
PYINSTALLER_OPTIONS = {
    'name': APP_NAME,
    'onefile': True,  # 打包成单个exe文件
    'windowed': True,  # Windows下隐藏控制台窗口
    'icon': None,  # 如果有图标文件，在这里指定路径
    'add_data': DATA_FILES,
    'hidden_import': HIDDEN_IMPORTS,
    'exclude_module': EXCLUDES,
    'clean': True,  # 清理临时文件
    'noconfirm': True,  # 不询问确认
}

def get_pyinstaller_command():
    """生成PyInstaller命令"""
    cmd_parts = ['pyinstaller']
    
    # 基本选项
    if PYINSTALLER_OPTIONS.get('onefile'):
        cmd_parts.append('--onefile')
    
    if PYINSTALLER_OPTIONS.get('windowed'):
        cmd_parts.append('--windowed')
    
    if PYINSTALLER_OPTIONS.get('clean'):
        cmd_parts.append('--clean')
    
    if PYINSTALLER_OPTIONS.get('noconfirm'):
        cmd_parts.append('--noconfirm')
    
    # 应用名称
    if PYINSTALLER_OPTIONS.get('name'):
        cmd_parts.extend(['--name', PYINSTALLER_OPTIONS['name']])
    
    # 图标
    if PYINSTALLER_OPTIONS.get('icon'):
        cmd_parts.extend(['--icon', PYINSTALLER_OPTIONS['icon']])
    
    # 数据文件
    for src, dst in PYINSTALLER_OPTIONS.get('add_data', []):
        if os.path.exists(src):
            cmd_parts.extend(['--add-data', f'{src};{dst}'])
        else:
            print(f"⚠️ 数据文件不存在: {src}")
    
    # 隐藏导入
    for module in PYINSTALLER_OPTIONS.get('hidden_import', []):
        cmd_parts.extend(['--hidden-import', module])
    
    # 排除模块
    for module in PYINSTALLER_OPTIONS.get('exclude_module', []):
        cmd_parts.extend(['--exclude-module', module])
    
    # 主脚本
    cmd_parts.append(MAIN_SCRIPT)
    
    return cmd_parts

def check_requirements():
    """检查打包前的要求"""
    print("=== 打包前检查 ===")
    
    # 检查主要文件
    required_files = [MAIN_SCRIPT, UI_FILE, 'resource_manager.py', 'utils.py']
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    # 检查photo文件夹
    photo_dir = Path('photo')
    if photo_dir.exists():
        print(f"✅ photo文件夹")
        
        # 检查必要的图片文件
        required_images = ['HHlogo.png', 'HHlogo整张水印.png']
        for img in required_images:
            img_path = photo_dir / img
            if img_path.exists():
                print(f"  ✅ {img}")
            else:
                print(f"  ❌ {img}")
                missing_files.append(f"photo/{img}")
        
        # 检查others文件夹
        others_dir = photo_dir / 'others'
        if others_dir.exists():
            others_files = list(others_dir.glob('*'))
            print(f"  ✅ others文件夹 ({len(others_files)} 个文件)")
        else:
            print(f"  ❌ others文件夹")
            missing_files.append("photo/others")
    else:
        print(f"❌ photo文件夹")
        missing_files.append("photo")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装")
        print("请运行: pip install pyinstaller")
        missing_files.append("PyInstaller")
    
    if missing_files:
        print(f"\n❌ 发现 {len(missing_files)} 个问题:")
        for item in missing_files:
            print(f"  - {item}")
        return False
    else:
        print("\n✅ 所有检查通过，可以开始打包")
        return True

def create_spec_file():
    """创建.spec文件"""
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{MAIN_SCRIPT}'],
    pathex=[],
    binaries=[],
    datas={DATA_FILES},
    hiddenimports={HIDDEN_IMPORTS},
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes={EXCLUDES},
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{APP_NAME}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    spec_filename = f"{APP_NAME}.spec"
    with open(spec_filename, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"✅ 已创建 {spec_filename}")
    return spec_filename

def main():
    """主函数"""
    print(f"=== {APP_NAME} v{APP_VERSION} 打包配置 ===")
    print(f"描述: {APP_DESCRIPTION}")
    print(f"作者: {APP_AUTHOR}")
    print()
    
    # 检查要求
    if not check_requirements():
        print("\n❌ 请解决上述问题后再进行打包")
        return False
    
    print("\n=== 打包命令 ===")
    cmd = get_pyinstaller_command()
    print("方法1 - 直接使用命令:")
    print(' '.join(cmd))
    
    print("\n方法2 - 使用spec文件:")
    spec_file = create_spec_file()
    print(f"pyinstaller {spec_file}")
    
    print("\n=== 打包说明 ===")
    print("1. 确保所有依赖已安装: pip install -r requirements.txt")
    print("2. 运行上述命令之一进行打包")
    print("3. 打包完成后，exe文件将在 dist/ 文件夹中")
    print("4. photo文件夹会自动包含在exe中，无需额外复制")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
