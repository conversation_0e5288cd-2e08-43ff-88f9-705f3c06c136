import os
import subprocess
import random
from pathlib import Path
from itertools import product, combinations
import re  # 用于解析策略名称中的匹配数量

# === 新增：FFmpeg路径自动选择函数 ===
def find_ffmpeg_path(predefined_paths=None):
    """
    自动查找有效FFmpeg路径
    :param predefined_paths: 预设路径列表（优先查找顺序）
    :return: (ffmpeg_path, ffprobe_path) 元组，找到则返回Path对象，否则None
    """
    if not predefined_paths:
        predefined_paths = [
            Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/"),
            Path("C:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/"),
            Path("E:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/"),
            Path("F:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/")
        ]
    
    for path in predefined_paths:
        ffmpeg_exe = path / "ffmpeg.exe"
        ffprobe_exe = path / "ffprobe.exe"
        if ffmpeg_exe.exists() and ffprobe_exe.exists():
            return (ffmpeg_exe, ffprobe_exe)
    return (None, None)

# === 核心接口定义 ===
def process_video_mix(
    main_file_paths,       # 前贴素材文件路径列表（字符串或Path对象）
    variant_file_paths,    # 后贴素材文件路径列表（字符串或Path对象）
    output_dir="output",   # 输出文件夹路径（字符串或Path对象）
    predefined_ffmpeg_paths=None  # 自定义FFmpeg查找路径（可选）
):
    """
    视频混剪核心处理函数
    :param main_file_paths: 前贴素材文件路径列表
    :param variant_file_paths: 后贴素材文件路径列表
    :param output_dir: 输出文件夹路径
    :param predefined_ffmpeg_paths: 自定义FFmpeg路径查找列表
    """
    # === 初始化路径处理 ===
    # 自动查找FFmpeg路径
    ffmpeg_path, ffprobe_path = find_ffmpeg_path(predefined_ffmpeg_paths)
    if not ffmpeg_path:
        print("\n❌ 致命错误：未找到FFmpeg或FFprobe可执行文件")
        input("按回车退出...")
        return
    
    # 转换路径对象
    output_dir = Path(output_dir).resolve()
    main_files = [Path(p) for p in main_file_paths]
    variant_files = [Path(p) for p in variant_file_paths]

    # === 初始化检查（拆分出来的独立函数） ===
    def init_check():
        print("=" * 50)
        print(f" 输出路径：{output_dir}")
        print(f" 使用FFmpeg：{ffmpeg_path.parent}")

        # 检查输出文件夹
        output_dir.mkdir(exist_ok=True)
        if not output_dir.is_dir():
            print(f"\n❌ 错误：无法创建输出文件夹：{output_dir}")
            input("按回车退出...")
            return None

        # 检查素材文件存在性
        for file_list, name in [(main_files, "前贴"), (variant_files, "后贴")]:
            invalid_files = [f for f in file_list if not f.exists() or f.suffix.lower() != ".mp4"]
            if invalid_files:
                print(f"\n❌ {name}素材中存在无效文件：")
                for f in invalid_files:
                    print(f"  - {f}")
                input("按回车退出...")
                return None
        print(f"\n🎞 素材加载完成：前贴{len(main_files)}个 | 后贴{len(variant_files)}个")

        # 检查视频格式
        main_inconsistent = check_video_format(main_files, "前贴")
        variant_inconsistent = check_video_format(variant_files, "后贴")

        if main_inconsistent or variant_inconsistent:
            input("按回车退出...")
            return None
        
        return main_files, variant_files

    # 执行初始化检查
    main, variant = init_check()
    if not main or not variant:
        return

    # === 智能分组和策略选择 ===
    n1, n2 = len(main), len(variant)
    if n1 <= 2 or n2 <= 2:
        main_groups = [main]
        variant_groups = [variant]
        strategies = generate_combination_strategies(main_groups, variant_groups, n1, n2)
        selected_name, selected_total = strategies[0]
        print(f"\n由于前贴或后贴数量小于等于2，仅提供全笛卡尔积策略。")
        print(f"\n✅ 已选择：{selected_name} → 预计生成 {selected_total} 个视频")
        group_pairs = list(product(main_groups, variant_groups))
        k = 1
    else:
        k = determine_optimal_k(n1, n2)  # 确定最优分组数
        main_groups = split_into_groups(main, k)  # 拆分前贴素材为k组
        variant_groups = split_into_groups(variant, k)  # 拆分后贴素材为k组

        # 显示分组结果
        print(f"\n📦 分组结果：k={k}组（强制≥3组）")
        print(f"  前贴分组：{[len(g) for g in main_groups]}（{'小数据允许单文件' if n1 <= 10 else '每组≥2'}）")
        print(f"  后贴分组：{[len(g) for g in variant_groups]}（{'小数据允许单文件' if n2 <= 10 else '每组≥2'}）")

        # 生成并显示所有策略
        strategies = generate_combination_strategies(main_groups, variant_groups, n1, n2)
        print("\n=== 可选组合策略（输入对应编号） ===")
        max_num_width = len(str(len(strategies)))  # 最大编号的宽度（用于对齐）
        max_name_width = max(len(s[0]) for s in strategies)  # 最大策略名称的宽度
        for idx, (name, total) in enumerate(strategies, start=1):
            # 格式化显示，确保等宽对齐
            num_str = f"{idx}.".ljust(max_num_width + 1)
            name_str = name.ljust(max_name_width)
            count_str = f"预计生成 {total} 个视频"
            print(f"  {num_str} {name_str}【{count_str}】")

        # 输入验证（确保输入有效编号）
        while True:
            try:
                choice = int(input(f"\n请输入组合策略编号（1-{len(strategies)}）: "))
                if 1 <= choice <= len(strategies):
                    break
                print("❌ 编号超出范围，请重新输入")
            except ValueError:
                print("❌ 请输入有效整数编号")

        selected_name, selected_total = strategies[choice - 1]
        print(f"\n✅ 已选择：{selected_name}")

        # 生成唯一组对
        group_pairs = []
        seen_pairs = set()  # 记录已生成的组对（前贴组索引, 后贴组索引），防止重复

        if "全笛卡尔积" in selected_name:
            # 生成所有可能的组对（前贴组 × 后贴组）
            group_pairs = list(product(main_groups, variant_groups))

        elif "一对一" in selected_name:
            # 按索引对应组对（前贴组0×后贴组0，组1×组1，依此类推）
            group_pairs = list(zip(main_groups, variant_groups))

        elif "一对" in selected_name:
            # 提取匹配数量
            match_count = int(re.search(r'一对(\d+)组', selected_name).group(1))
            for i in range(k):  # 遍历每个前贴组索引
                # 计算后贴组索引列表（循环选取match_count个组）
                v_indices = [(i + j) % k for j in range(match_count)]
                for v_idx in v_indices:
                    if (i, v_idx) not in seen_pairs:
                        seen_pairs.add((i, v_idx))
                        group_pairs.append((main_groups[i], variant_groups[v_idx]))

    # === 合并视频 ===
    print("\n" + "=" * 60)
    print("（注：同名文件/非法字符将被跳过，实际数量可能减少）")
    print("=" * 60)

    input("\n⚠️ 注意：需NVIDIA显卡支持GPU加速（按回车开始）")
    merge_videos(group_pairs, k, selected_name)  # 合并视频

    output_count = len(list(output_dir.glob("*.mp4")))
    print(f"\n🎉 任务完成！\n  实际生成：{output_count}个视频")
    print(f"📁 输出路径：{output_dir.resolve()}")

# 检查视频分辨率和帧率
def check_video_format(videos, folder_name):
    resolution_count = {}
    fps_count = {}
    inconsistent_resolution = []
    inconsistent_fps = []

    for video in videos:
        info = get_video_info(video)
        if info:
            resolution = f"{info['width']}x{info['height']}"
            fps = info['fps']

            resolution_count[resolution] = resolution_count.get(resolution, 0) + 1
            fps_count[fps] = fps_count.get(fps, 0) + 1

    # 找出少数不一致的分辨率
    if len(resolution_count) > 1:
        sorted_resolutions = sorted(resolution_count.items(), key=lambda x: x[1])
        minority_resolutions = []
        if sorted_resolutions[0][1] < sorted_resolutions[-1][1]:
            minority_resolutions = [sorted_resolutions[0][0]]
        elif sorted_resolutions[0][1] == sorted_resolutions[-1][1] and sorted_resolutions[0][0] in ["720x1280", "720x960"]:
            minority_resolutions = [sorted_resolutions[0][0]]

        for video in videos:
            info = get_video_info(video)
            if info:
                resolution = f"{info['width']}x{info['height']}"
                if resolution in minority_resolutions:
                    inconsistent_resolution.append(f"{video.name} + {resolution}")

    # 找出少数不一致的帧率
    if len(fps_count) > 1:
        sorted_fps = sorted(fps_count.items(), key=lambda x: x[1])
        minority_fps = []
        if sorted_fps[0][1] < sorted_fps[-1][1]:
            minority_fps = [sorted_fps[0][0]]
        elif sorted_fps[0][1] == sorted_fps[-1][1] and sorted_fps[0][0] == 30:
            minority_fps = [sorted_fps[0][0]]

        for video in videos:
            info = get_video_info(video)
            if info:
                fps = info['fps']
                if fps in minority_fps:
                    inconsistent_fps.append(f"{video.name} + {fps}")

    # 优化输出格式：换行显示，每个素材单独一行
    if inconsistent_resolution:
        print("❌")
        print(f"{folder_name}中存在视频格式不一致素材（分辨率）：")
        for item in inconsistent_resolution:
            print(f"  - {item}")
    
    if inconsistent_fps:
        print("❌")
        print(f"{folder_name}中存在视频格式不一致素材（帧率）：")
        for item in inconsistent_fps:
            print(f"  - {item}")

    return inconsistent_resolution or inconsistent_fps

# 获取视频信息（增强解析逻辑）
def get_video_info(file_path):
    cmd = [str(FFPROBE_PATH), "-v", "error", "-select_streams", "v:0",
           "-show_entries", "stream=width,height,r_frame_rate,codec_name",
           "-of", "csv=p=0", str(file_path)]
    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                text=True, timeout=10)
        data = result.stdout.strip().split(',')
        
        # 尝试智能解析数据
        width, height, fps = None, None, None
        width_candidates = []
        height_candidates = []
        fps_candidates = []
        
        for item in data:
            # 尝试解析为宽度或高度
            try:
                num = int(item)
                if num > 100 and num < 5000:  # 合理的分辨率范围
                    if width is None:
                        width = num
                    elif height is None:
                        height = num
                    else:
                        # 如果已经有宽度和高度，可能是多余的数据
                        pass
            except ValueError:
                # 尝试解析为帧率
                if '/' in item:
                    try:
                        num, den = item.split('/')
                        fps_val = float(num) / float(den)
                        if 10 < fps_val < 120:  # 合理的帧率范围
                            fps = fps_val
                    except:
                        pass
                elif item.replace('.', '', 1).isdigit():
                    fps_val = float(item)
                    if 10 < fps_val < 120:
                        fps = fps_val
        
        # 验证是否成功解析
        if width and height and fps:
            return {"width": width, "height": height, "fps": fps}
        else:
            print(f"⚠️ 解析失败：{file_path.name}，无法提取有效视频信息")
            return None
            
    except Exception as e:
        print(f"⚠️ 解析参数失败：{file_path.name} → {str(e)[:50]}")
        return None

# 计算两个数的公共因数（用于确定分组数，最小分组数为3）
def determine_common_factors(n1, n2, min_k=3):
    factors1 = set(i for i in range(min_k, n1 + 1) if n1 % i == 0)
    factors2 = set(i for i in range(min_k, n2 + 1) if n2 % i == 0)
    return sorted(factors1 & factors2, reverse=True)  # 返回降序排列的公共因数

# 确定最优分组数k（确保每组文件数尽可能均衡）
def determine_optimal_k(n1, n2):
    is_small1, is_small2 = n1 <= 10, n2 <= 10
    kmax1 = n1 if is_small1 else n1 // 2  # 小数据（≤10个）允许全部分组，大数据最多分一半
    kmax2 = n2 if is_small2 else n2 // 2
    kmax = min(kmax1, kmax2)  # 取两者的最小最大分组数

    if kmax < 3:  # 至少分3组
        print("❌ 无法满足组数≥3，请增加素材数量（至少3个/文件夹）")
        input("按回车退出...")
        exit(1)

    common_factors = determine_common_factors(n1, n2)
    candidates = common_factors if common_factors else list(range(3, kmax + 1))  # 优先使用公共因数，否则顺序生成3到kmax的分组数

    best_k, min_diff = None, float('inf')
    for k in candidates:
        g1 = _split(n1, k, is_small1)  # 拆分前贴素材为k组
        g2 = _split(n2, k, is_small2)  # 拆分后贴素材为k组
        if not g1 or not g2:  # 确保分组有效（大数据组每组至少2个文件）
            continue
        current_diff = max(max(g1), max(g2)) - min(min(g1), min(g2))  # 计算分组后最大组与最小组的差异
        # 选择差异最小的k，差异相同时选择更大的k（更多分组）
        if current_diff < min_diff or (current_diff == min_diff and k > best_k):
            best_k, min_diff = k, current_diff
    return best_k

# 辅助函数：将n个文件拆分为k组，小数据允许单文件，大数据每组至少2个
def _split(n, k, is_small):
    a, r = divmod(n, k)
    groups = [a + 1] * r + [a] * (k - r)  # 平均分配，余数在前（例如8个文件分4组：2,2,2,2）
    if not is_small and any(g < 2 for g in groups):  # 大数据（>10个）要求每组至少2个文件
        return None
    return groups

# 实际拆分文件列表为k组（根据分组大小生成切片）
def split_into_groups(items, k):
    a, r = divmod(len(items), k)
    return [items[i * a + min(i, r): (i + 1) * a + min(i + 1, r)] for i in range(k)]

# 生成所有组合策略（核心逻辑：计算每种策略的组合数）
def generate_combination_strategies(main_groups, variant_groups, main_count, variant_count):
    k = len(main_groups)
    m_lens = [len(g) for g in main_groups]  # 前贴各组文件数（例如[2,2,2,2]）
    v_lens = [len(g) for g in variant_groups]  # 后贴各组文件数（例如[3,3,3,3]）
    strategies = []

    # 策略1：全笛卡尔积（所有前贴组与后贴组自由组合，生成所有可能的组对）
    total_full = sum(m * v for m in m_lens for v in v_lens)
    strategies.append(("全笛卡尔积", total_full))

    if main_count > 2 and variant_count > 2:
        # 策略2：一对一顺序匹配（前贴组i仅与后贴组i组合，组号对应）
        total_one_to_one = sum(m * v for m, v in zip(m_lens, v_lens))
        strategies.append(("一对一", total_one_to_one))

        # 策略3及以后：一对多顺序循环匹配（每组匹配后贴的N个不同组，按顺序循环）
        for match_count in range(2, k):
            total = 0
            for i in range(k):  # 遍历每个前贴组
                # 计算后贴组索引列表（循环选取match_count个组）
                v_indices = [(i + j) % k for j in range(match_count)]
                # 计算这N个后贴组的文件总数
                v_total = sum(v_lens[j] for j in v_indices)
                # 前贴组i的文件数 × 后贴组文件总数
                total += m_lens[i] * v_total

            strategies.append((
                f"一对{match_count}组",
                total
            ))

        # 删减法策略：基于非一对一策略生成（每个策略删除2*k个组合，k为分组数）
        non_one_to_one_strategies = [s for s in strategies if "一对一" not in s[0]]  # 排除一对一策略
        for (name, total) in non_one_to_one_strategies:
            delete_count = 2 * k  # 修改为两倍的分组数
            new_total = total - delete_count
            if new_total > 0:  # 确保删除后还有剩余组合
                strategies.append((
                    f"删减{name}",
                    new_total
                ))

    # 按生成数量排序（升序）
    strategies.sort(key=lambda x: x[1])
    return strategies

# 视频合并核心函数（包含组对合并、删减法、错误处理）
def merge_videos(groups, k, selected_name):
    total = sum(len(m) * len(v) for m, v in groups)  # 计算总组合数（未跳过同名/非法字符的数量）
    print(f"\n🎥 开始合并：{total}个视频（GPU加速版）")

    all_combinations = []  # 存储所有合法组合（排除同名和非法字符）
    for m_group, v_group in groups:
        for m_file in m_group:
            for v_file in v_group:
                # 跳过同名文件（前后贴stem相同，例如"素材A.mp4"和"素材A.mp4"）
                if m_file.stem == v_file.stem:
                    print(f"  ⚠️ 跳过同名文件：{m_file.name} + {v_file.name}")
                    continue
                # 跳过含非法字符的文件名（Windows不允许的字符）
                invalid_chars = '\\/:*?"<>|'
                if any(c in invalid_chars for c in f"{m_file.name}{v_file.name}"):
                    print(f"  ⚠️ 跳过含非法字符：{m_file.name} + {v_file.name}")
                    continue
                all_combinations.append((m_file, v_file))  # 添加合法组合

    # 处理删减法策略（随机删除2*k个不相邻的组合）
    if "删减" in selected_name:
        delete_count = 2 * k  # 修改为两倍的分组数
        if len(all_combinations) <= delete_count:
            print(f"❌ 删减法警告：仅{len(all_combinations)}个组合，不足删除{delete_count}个，跳过删除")
        else:
            # 使用Fisher - Yates算法随机选择删除索引
            indices = list(range(len(all_combinations)))
            for i in range(len(indices) - 1, len(indices) - delete_count - 1, -1):
                j = random.randint(0, i)
                indices[i], indices[j] = indices[j], indices[i]  # 随机交换位置，确保随机性

            # 确保删除的索引不连续（避免集中删除相邻组合）
            deleted_indices = sorted(indices[-delete_count:])
            for i in range(1, len(deleted_indices)):
                if deleted_indices[i] == deleted_indices[i - 1] + 1:
                    deleted_indices[i] = (deleted_indices[i] + 1) % len(all_combinations)  # 简单处理不连续

            # 过滤掉被删除的组合
            all_combinations = [combo for idx, combo in enumerate(all_combinations) if idx not in deleted_indices]

    # 生成视频文件（使用FFmpeg合并前后贴视频）
    for m_file, v_file in all_combinations:
        output = OUTPUT_DIR / f"{m_file.stem}{v_file.stem}.mp4"
        filter_complex = [
            f"[0:v] scale=1080:-1, setsar=1 [v0];",  # 前贴视频缩放至1080p，保持宽高比
            f"[1:v] scale=1080:-1, setsar=1 [v1];",  # 后贴视频缩放至1080p，保持宽高比
            "[v0][0:a][v1][1:a] concat=n=2:v=1:a=1 [outv][outa]"  # 合并视频和音频流，n = 2表示合并2个输入
        ]

        cmd = [
            str(FFMPEG_PATH), "-y",  # 覆盖已存在文件（-y参数）
            "-hwaccel", "cuda",  # 使用CUDA硬件加速（需NVIDIA显卡）
            "-i", str(m_file),  # 输入前贴视频文件
            "-i", str(v_file),  # 输入后贴视频文件
            "-filter_complex", "".join(filter_complex),  # 应用滤镜处理
            "-map", "[outv]", "-c:v", "h264_nvenc", "-preset", "p2", "-b:v", "10M",  # 视频编码参数（H.264，码率10M）
            "-map", "[outa]", "-c:a", "aac", "-b:a", "192k",  # 音频编码参数（AAC，码率192k）
            str(output)  # 输出文件路径
        ]

        try:
            subprocess.run(cmd, check=True, stdout=subprocess.DEVNULL, stderr=subprocess.STDOUT)
            print(f"  ✅ 完成：{output.name}")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ 失败：{m_file.name}+{v_file.name} → 命令执行失败，返回码: {e.returncode}")
        except Exception as e:
            print(f"  ❌ 失败：{m_file.name}+{v_file.name} → {str(e)[:50]}")

# 程序入口（主函数）
if __name__ == "__main__":
    try:
        # 示例用法：从命令行参数获取路径
        import sys
        
        # 默认路径
        main_files = list(Path("前贴").glob("*.mp4")) if Path("前贴").exists() else []
        variant_files = list(Path("后贴").glob("*.mp4")) if Path("后贴").exists() else []
        output_dir = "output"
        
        # 处理命令行参数
        if len(sys.argv) > 1:
            print("使用命令行参数指定路径...")
            # 假设参数格式：python script.py 前贴路径 后贴路径 输出路径
            if len(sys.argv) >= 4:
                main_dir = Path(sys.argv[1])
                variant_dir = Path(sys.argv[2])
                output_dir = Path(sys.argv[3])
                
                if main_dir.is_dir():
                    main_files = list(main_dir.glob("*.mp4"))
                if variant_dir.is_dir():
                    variant_files = list(variant_dir.glob("*.mp4"))
            else:
                print("参数不足，使用默认路径...")
        
        # 转换为文件路径列表
        main_file_paths = [str(f) for f in main_files]
        variant_file_paths = [str(f) for f in variant_files]
        
        if not main_file_paths or not variant_file_paths:
            print("❌ 未找到素材文件，请确保前贴和后贴文件夹中有MP4文件")
            sys.exit(1)
        
        # 调用核心处理函数
        process_video_mix(
            main_file_paths=main_file_paths,
            variant_file_paths=variant_file_paths,
            output_dir=output_dir
        )
        
    except Exception as e:
        print(f"\n发生未知错误：{str(e)}")
        input("按任意键退出...")