#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试水印预览功能
"""

import os
import sys
import subprocess
from resource_manager import get_photo_path

def test_ai_watermark():
    """测试AI水印功能"""
    print("=== 测试AI水印功能 ===")
    
    # 检查AI图片是否存在
    ai_image = get_photo_path("AI生成.png")
    print(f"AI图片路径: {ai_image}")
    print(f"AI图片存在: {os.path.exists(ai_image)}")
    
    if os.path.exists(ai_image):
        print(f"AI图片大小: {os.path.getsize(ai_image)} bytes")
    
    # 检查FFmpeg
    ffmpeg_path = "D:\\FFmpeg\\ffmpeg-2025-03-31-git-35c091f4b7-full_build\\bin\\ffmpeg.exe"
    if os.path.exists(ffmpeg_path):
        print(f"FFmpeg存在: {ffmpeg_path}")
    else:
        print("FFmpeg不存在")
        return
    
    # 创建测试视频帧（如果有测试视频的话）
    test_video = "test_video.mp4"  # 需要用户提供测试视频
    if not os.path.exists(test_video):
        print(f"测试视频不存在: {test_video}")
        print("请在当前目录放置一个名为test_video.mp4的测试视频文件")
        return
    
    # 测试提取原始帧
    output_frame = "test_frame.jpg"
    cmd = [
        ffmpeg_path,
        "-i", test_video,
        "-ss", "1",
        "-vframes", "1",
        "-y", output_frame
    ]
    
    print("测试提取原始帧...")
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=False,
            timeout=10
        )
        print(f"提取原始帧返回码: {result.returncode}")
        if result.returncode == 0 and os.path.exists(output_frame):
            print(f"原始帧提取成功，大小: {os.path.getsize(output_frame)} bytes")
        else:
            print("原始帧提取失败")
            return
    except Exception as e:
        print(f"提取原始帧异常: {str(e)}")
        return
    
    # 测试AI水印
    if os.path.exists(ai_image):
        watermark_output = "test_watermark.jpg"
        cmd = [
            ffmpeg_path,
            "-i", output_frame,
            "-i", ai_image,
            "-filter_complex", "[1:v]scale=220:-1[ai];[0:v][ai]overlay=W-w-48:48",
            "-y", watermark_output
        ]
        
        print("测试AI水印...")
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=False,
                timeout=10
            )
            print(f"AI水印返回码: {result.returncode}")
            if result.returncode == 0 and os.path.exists(watermark_output):
                print(f"AI水印成功，大小: {os.path.getsize(watermark_output)} bytes")
            else:
                print("AI水印失败")
                if result.stderr:
                    print(f"错误信息: {result.stderr}")
        except Exception as e:
            print(f"AI水印异常: {str(e)}")
    
    # 清理测试文件
    for file in [output_frame, watermark_output]:
        try:
            if os.path.exists(file):
                os.remove(file)
        except:
            pass

if __name__ == "__main__":
    test_ai_watermark()
