#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
便携授权码功能演示脚本
展示完整的便携授权码生成、验证和使用流程
"""

import sys
import os
from datetime import datetime, timedelta

def demo_step_1_generate():
    """演示步骤1：生成便携授权码"""
    print("=" * 60)
    print("步骤1：生成便携授权码")
    print("=" * 60)
    
    try:
        from portable_auth_generator import PortableAuthGenerator
        
        generator = PortableAuthGenerator()
        
        # 生成多个不同时间段的授权码
        auth_codes = []
        
        # 授权码1：当前日期开始，3天有效期
        start_date1 = datetime.now().date()
        end_date1 = start_date1 + timedelta(days=3)
        code1 = generator.generate_portable_auth_code(start_date1, end_date1, "用户A")
        auth_codes.append((code1, start_date1, end_date1, "用户A"))
        
        # 授权码2：5天后开始，2天有效期（续期示例）
        start_date2 = start_date1 + timedelta(days=5)
        end_date2 = start_date2 + timedelta(days=2)
        code2 = generator.generate_portable_auth_code(start_date2, end_date2, "用户A")
        auth_codes.append((code2, start_date2, end_date2, "用户A"))
        
        # 授权码3：不同用户
        start_date3 = datetime.now().date()
        end_date3 = start_date3 + timedelta(days=1)
        code3 = generator.generate_portable_auth_code(start_date3, end_date3, "用户B")
        auth_codes.append((code3, start_date3, end_date3, "用户B"))
        
        print("✅ 成功生成以下便携授权码：\n")
        
        for i, (code, start, end, user) in enumerate(auth_codes, 1):
            print(f"授权码{i} ({user}):")
            print(f"  有效期: {start} 至 {end}")
            print(f"  授权码: {code}")
            print()
        
        return auth_codes
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return []

def demo_step_2_verify(auth_codes):
    """演示步骤2：验证便携授权码"""
    print("=" * 60)
    print("步骤2：验证便携授权码")
    print("=" * 60)
    
    try:
        from portable_auth_verifier import PortableAuthVerifier
        
        verifier = PortableAuthVerifier("演示应用")
        
        print("验证各个授权码：\n")
        
        valid_codes = []
        for i, (code, start, end, user) in enumerate(auth_codes, 1):
            print(f"验证授权码{i} ({user}):")
            valid, auth_data, message = verifier.verify_portable_auth_code(code)
            
            if valid:
                print(f"  ✅ {message}")
                valid_codes.append((code, start, end, user))
            else:
                print(f"  ❌ {message}")
            print()
        
        # 显示当前有效的授权码
        print("当前有效的便携授权码汇总：")
        active_auths = verifier.get_active_portable_auths()
        
        if active_auths:
            for auth in active_auths:
                print(f"  - {auth['start_date']} 至 {auth['end_date']} (剩余{auth['days_left']}天)")
                if auth['user']:
                    print(f"    用户: {auth['user']}")
        else:
            print("  无有效授权码")
        
        return valid_codes
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return []

def demo_step_3_integration():
    """演示步骤3：与主授权系统集成"""
    print("\n" + "=" * 60)
    print("步骤3：与主授权系统集成测试")
    print("=" * 60)
    
    try:
        from machine_code_verifier import check_authorization_only
        
        # 检查完整的授权状态（机器码 + 便携授权码）
        valid, message, machine_id, md5_value = check_authorization_only("演示应用")
        
        print(f"当前机器码: {machine_id}")
        print(f"机器码MD5: {md5_value}")
        print(f"综合授权状态: {'✅ 通过' if valid else '❌ 失败'}")
        print(f"授权消息: {message}")
        
        return valid
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def demo_step_4_time_rollback():
    """演示步骤4：时间回退检测"""
    print("\n" + "=" * 60)
    print("步骤4：时间回退检测演示")
    print("=" * 60)
    
    try:
        from portable_auth_verifier import PortableAuthVerifier
        
        verifier = PortableAuthVerifier("时间测试应用")
        
        # 检查时间回退检测
        has_valid, message = verifier.has_valid_portable_auth()
        print(f"时间验证结果: {message}")
        
        # 显示时间记录文件位置
        print(f"时间记录文件: {verifier.portable_time_file}")
        print(f"授权记录文件: {verifier.portable_auth_file}")
        
        if os.path.exists(verifier.portable_time_file):
            print("✅ 时间记录文件存在，时间回退检测功能正常")
        else:
            print("⚠️ 时间记录文件不存在，将在首次使用时创建")
        
        return True
        
    except Exception as e:
        print(f"❌ 时间回退检测失败: {e}")
        return False

def demo_step_5_gui():
    """演示步骤5：GUI界面展示"""
    print("\n" + "=" * 60)
    print("步骤5：GUI界面演示")
    print("=" * 60)
    
    try:
        print("正在启动便携授权码生成工具GUI...")
        print("您可以在GUI中:")
        print("1. 设置授权码的有效期")
        print("2. 指定用户标识")
        print("3. 生成并复制授权码")
        print("4. 验证已有的授权码")
        print("\n按Ctrl+C可以跳过GUI演示")
        
        # 启动GUI（非阻塞）
        import subprocess
        import sys
        
        # 使用subprocess启动GUI，这样不会阻塞当前进程
        process = subprocess.Popen([
            sys.executable, 
            "portable_auth_generator.py"
        ], cwd=os.getcwd())
        
        print(f"✅ GUI已启动 (进程ID: {process.pid})")
        print("请在GUI中测试功能，完成后关闭GUI窗口")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户跳过GUI演示")
        return True
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        return False

def main():
    """主演示函数"""
    print("便携授权码功能完整演示")
    print("=" * 60)
    print("本演示将展示便携授权码的完整功能：")
    print("1. 生成便携授权码")
    print("2. 验证便携授权码")
    print("3. 与主授权系统集成")
    print("4. 时间回退检测")
    print("5. GUI界面展示")
    print("=" * 60)
    
    try:
        # 步骤1：生成授权码
        auth_codes = demo_step_1_generate()
        if not auth_codes:
            print("❌ 演示失败：无法生成授权码")
            return 1
        
        # 步骤2：验证授权码
        valid_codes = demo_step_2_verify(auth_codes)
        
        # 步骤3：集成测试
        integration_ok = demo_step_3_integration()
        
        # 步骤4：时间回退检测
        time_check_ok = demo_step_4_time_rollback()
        
        # 步骤5：GUI演示
        gui_ok = demo_step_5_gui()
        
        # 总结
        print("\n" + "=" * 60)
        print("演示总结")
        print("=" * 60)
        print(f"生成授权码: {'✅' if auth_codes else '❌'}")
        print(f"验证授权码: {'✅' if valid_codes else '❌'}")
        print(f"系统集成: {'✅' if integration_ok else '❌'}")
        print(f"时间检测: {'✅' if time_check_ok else '❌'}")
        print(f"GUI界面: {'✅' if gui_ok else '❌'}")
        
        if all([auth_codes, valid_codes, integration_ok, time_check_ok, gui_ok]):
            print("\n🎉 便携授权码功能演示完成！所有功能正常工作。")
            print("\n📋 使用说明：")
            print("- 运行 portable_auth_generator.py 生成授权码")
            print("- 在软件未授权时输入便携授权码即可临时使用")
            print("- 详细说明请查看 便携授权码使用说明.txt")
            return 0
        else:
            print("\n⚠️ 部分功能存在问题，请检查错误信息")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断演示")
        return 0
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
