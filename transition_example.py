#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转场功能使用示例
演示如何正确使用优化后的转场功能
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 转场合并 import merge_videos, parse_transition_type

def example_basic_transition():
    """基础转场示例"""
    print("📝 基础转场示例")
    print("-" * 30)
    
    # 假设你有两个视频文件
    video_files = [
        "video1.mp4",
        "video2.mp4"
    ]
    
    # 检查文件是否存在
    for video in video_files:
        if not os.path.exists(video):
            print(f"⚠️  文件不存在: {video}")
            print("请确保有测试视频文件，或运行 test_transition.py 创建测试文件")
            return False
    
    # 使用fade转场效果
    output_file = "merged_with_fade.mp4"
    
    print(f"🎬 合并视频: {' + '.join(video_files)}")
    print(f"📤 输出文件: {output_file}")
    print(f"🔄 转场效果: fade (淡入淡出)")
    print(f"⏱️  转场时长: 1.5秒")
    
    success = merge_videos(
        videos=video_files,
        output=output_file,
        transition='fade',  # 使用单引号，这是关键修复
        trans_duration=1.5
    )
    
    if success:
        print("✅ 转场合并成功！")
        return True
    else:
        print("❌ 转场合并失败")
        return False

def example_chinese_transition():
    """中文转场描述示例"""
    print("\n📝 中文转场描述示例")
    print("-" * 30)
    
    video_files = ["video1.mp4", "video2.mp4"]
    
    # 检查文件是否存在
    for video in video_files:
        if not os.path.exists(video):
            print(f"⚠️  文件不存在: {video}")
            return False
    
    # 使用中文转场描述
    chinese_transitions = [
        "【柔】【叠化】fade",
        "【向左擦除】wipeleft", 
        "【柔】【圆形展开】circleopen",
        "【时钟擦除】radial"
    ]
    
    for transition_text in chinese_transitions:
        # 解析转场类型
        transition_name = parse_transition_type(transition_text)
        output_file = f"merged_{transition_name}.mp4"
        
        print(f"\n🔄 转场: {transition_text}")
        print(f"📤 输出: {output_file}")
        print(f"🎯 解析为: {transition_name}")
        
        success = merge_videos(
            videos=video_files,
            output=output_file,
            transition=transition_text,  # 直接使用中文描述
            trans_duration=1.0
        )
        
        if success:
            print("✅ 成功")
        else:
            print("❌ 失败")

def example_random_transition():
    """随机转场示例"""
    print("\n📝 随机转场示例")
    print("-" * 30)
    
    video_files = ["video1.mp4", "video2.mp4"]
    
    # 检查文件是否存在
    for video in video_files:
        if not os.path.exists(video):
            print(f"⚠️  文件不存在: {video}")
            return False
    
    # 随机转场选项
    random_options = [
        "【【全随机】】",
        "【【柔 · 随机】】", 
        "【【硬 · 随机】】"
    ]
    
    for i, random_type in enumerate(random_options, 1):
        print(f"\n🎲 随机转场 {i}: {random_type}")
        
        # 每次调用都会得到不同的随机转场
        for j in range(3):
            transition_name = parse_transition_type(random_type)
            print(f"  第{j+1}次随机结果: {transition_name}")
        
        # 实际合并（只做一次）
        output_file = f"merged_random_{i}.mp4"
        success = merge_videos(
            videos=video_files,
            output=output_file,
            transition=random_type,
            trans_duration=0.8
        )
        
        if success:
            print(f"✅ 随机转场合并成功: {output_file}")
        else:
            print(f"❌ 随机转场合并失败")

def example_multiple_videos():
    """多视频转场示例"""
    print("\n📝 多视频转场示例")
    print("-" * 30)
    
    # 假设有多个视频文件
    video_files = [
        "video1.mp4",
        "video2.mp4", 
        "video3.mp4"  # 如果有第三个视频
    ]
    
    # 只使用存在的文件
    existing_files = [v for v in video_files if os.path.exists(v)]
    
    if len(existing_files) < 2:
        print("⚠️  需要至少2个视频文件")
        return False
    
    print(f"🎬 合并 {len(existing_files)} 个视频")
    print(f"📁 文件: {existing_files}")
    
    # 使用平滑的转场效果
    output_file = "merged_multiple.mp4"
    
    success = merge_videos(
        videos=existing_files,
        output=output_file,
        transition="【柔】【向左擦除】smoothleft",
        trans_duration=1.2
    )
    
    if success:
        print(f"✅ 多视频转场合并成功: {output_file}")
        return True
    else:
        print("❌ 多视频转场合并失败")
        return False

def show_available_transitions():
    """显示所有可用的转场效果"""
    print("\n📋 所有可用的转场效果")
    print("=" * 50)
    
    transitions = [
        ("【【全随机】】", "从所有转场中随机选择"),
        ("【【柔 · 随机】】", "从柔和转场中随机选择"),
        ("【【硬 · 随机】】", "从硬切转场中随机选择"),
        ("【柔】【叠化】fade", "淡入淡出"),
        ("【向左擦除】wipeleft", "向左擦除"),
        ("【向右擦除】wiperight", "向右擦除"),
        ("【向上擦除】wipeup", "向上擦除"),
        ("【向下擦除】wipedown", "向下擦除"),
        ("【柔】【向左擦除】smoothleft", "柔和向左擦除"),
        ("【柔】【向右擦除】smoothright", "柔和向右擦除"),
        ("【柔】【向上擦除】smoothup", "柔和向上擦除"),
        ("【柔】【向下擦除】smoothdown", "柔和向下擦除"),
        ("【向左滑动】slideleft", "向左滑动"),
        ("【向右滑动】slideright", "向右滑动"),
        ("【向上滑动】slideup", "向上滑动"),
        ("【向下滑动】slidedown", "向下滑动"),
        ("【柔】【圆形展开】circleopen", "圆形展开"),
        ("【柔】【圆形闭合】circleclose", "圆形闭合"),
        ("【柔】【垂直展开】vertopen", "垂直展开"),
        ("【柔】【垂直闭合】vertclose", "垂直闭合"),
        ("【柔】【水平展开】horzopen", "水平展开"),
        ("【柔】【水平闭合】horzclose", "水平闭合"),
        ("【柔】【景深转场】distance", "景深转场"),
        ("【时钟擦除】radial", "时钟擦除"),
        ("【像素模糊】pixelize", "像素模糊"),
        ("【放大转场】zoomin", "放大转场"),
        ("【柔】【向左上擦除】diagtl", "柔和向左上擦除"),
        ("【柔】【向右上擦除】diagtr", "柔和向右上擦除"),
        ("【柔】【向左下擦除】diagbl", "柔和向左下擦除"),
        ("【柔】【向右下擦除】diagbr", "柔和向右下擦除"),
        ("【向左上擦除】wipetl", "向左上擦除"),
        ("【向右上擦除】wipetr", "向右上擦除"),
        ("【向左下擦除】wipebl", "向左下擦除"),
        ("【向右下擦除】wipebr", "向右下擦除"),
        ("【向左百叶窗】hlslice", "向左百叶窗"),
        ("【向右百叶窗】hrslice", "向右百叶窗"),
        ("【向上百叶窗】vuslice", "向上百叶窗"),
        ("【向下百叶窗】vdslice", "向下百叶窗"),
        ("【向左滑刺】hlwind", "向左滑刺"),
        ("【向右滑刺】hrwind", "向右滑刺"),
        ("【向上滑刺】vuwind", "向上滑刺"),
        ("【向下滑刺】vdwind", "向下滑刺")
    ]
    
    for i, (code, desc) in enumerate(transitions, 1):
        parsed = parse_transition_type(code)
        print(f"{i:2d}. {desc}")
        print(f"    代码: {code}")
        print(f"    解析: {parsed}")
        print()

if __name__ == "__main__":
    print("🎬 转场功能使用示例")
    print("=" * 50)
    
    # 显示所有可用转场
    show_available_transitions()
    
    # 运行示例（如果有测试视频文件）
    if os.path.exists("video1.mp4") and os.path.exists("video2.mp4"):
        print("\n🚀 运行转场示例...")
        
        example_basic_transition()
        example_chinese_transition() 
        example_random_transition()
        example_multiple_videos()
        
        print("\n🎉 所有示例运行完成！")
    else:
        print("\n💡 提示:")
        print("1. 运行 test_transition.py 创建测试视频")
        print("2. 或者将你的视频文件重命名为 video1.mp4, video2.mp4")
        print("3. 然后再运行此示例脚本")
