#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频分辨率检测功能
"""

import sys
import os
import json

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_metadata_structure():
    """测试get_video_metadata返回的数据结构"""
    try:
        from utils import get_video_metadata
        
        print("=== 测试视频元数据结构 ===")
        
        # 查找一个测试视频文件
        test_video = None
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
        
        # 在当前目录查找视频文件
        for file in os.listdir('.'):
            if any(file.lower().endswith(ext) for ext in video_extensions):
                test_video = file
                break
        
        if not test_video:
            print("❌ 当前目录没有找到视频文件用于测试")
            print("💡 请将一个视频文件放在当前目录中进行测试")
            return False
        
        print(f"📹 测试视频: {test_video}")
        
        # 获取元数据
        metadata = get_video_metadata(test_video)
        
        if not metadata:
            print("❌ 无法获取视频元数据")
            return False
        
        print("✅ 成功获取视频元数据")
        print(f"📊 元数据结构:")
        
        # 打印元数据的主要键
        print(f"  主要键: {list(metadata.keys())}")
        
        # 检查streams
        if 'streams' in metadata:
            print(f"  streams数量: {len(metadata['streams'])}")
            if metadata['streams']:
                video_stream = metadata['streams'][0]
                print(f"  第一个stream的键: {list(video_stream.keys())}")
                
                # 检查分辨率信息
                width = video_stream.get('width')
                height = video_stream.get('height')
                print(f"  分辨率: {width}x{height}")
                
                # 检查其他信息
                codec = video_stream.get('codec_name')
                duration = video_stream.get('duration')
                print(f"  编码器: {codec}")
                print(f"  时长: {duration}秒")
        
        # 检查format
        if 'format' in metadata:
            format_info = metadata['format']
            print(f"  format键: {list(format_info.keys())}")
            duration = format_info.get('duration')
            size = format_info.get('size')
            print(f"  总时长: {duration}秒")
            print(f"  文件大小: {size}字节")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_resolution_validation():
    """测试分辨率验证逻辑"""
    try:
        from utils import get_video_metadata
        
        print("\n=== 测试分辨率验证逻辑 ===")
        
        def validate_video_resolution(video_file):
            """模拟修复后的分辨率验证逻辑"""
            try:
                metadata = get_video_metadata(video_file)
                if metadata and 'streams' in metadata:
                    video_stream = metadata['streams'][0]
                    width = video_stream.get('width', 0)
                    height = video_stream.get('height', 0)
                    print(f"  检测到分辨率: {width}x{height}")
                    if width != 1080 or height != 1920:
                        return False, f"{width}x{height}"
                    return True, f"{width}x{height}"
                else:
                    return False, "无法获取分辨率"
            except Exception as e:
                return False, f"解析失败: {str(e)}"
        
        # 查找视频文件进行测试
        video_files = []
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
        
        for file in os.listdir('.'):
            if any(file.lower().endswith(ext) for ext in video_extensions):
                video_files.append(file)
        
        if not video_files:
            print("❌ 没有找到视频文件用于测试")
            return False
        
        print(f"📹 找到 {len(video_files)} 个视频文件:")
        
        for video_file in video_files:
            print(f"\n测试文件: {video_file}")
            is_valid, resolution_info = validate_video_resolution(video_file)
            
            if is_valid:
                print(f"  ✅ 分辨率符合要求: {resolution_info}")
            else:
                print(f"  ❌ 分辨率不符合要求: {resolution_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 视频分辨率检测测试 ===")
    
    # 测试元数据结构
    structure_ok = test_metadata_structure()
    
    # 测试分辨率验证
    validation_ok = test_resolution_validation()
    
    print("\n=== 测试结果 ===")
    if structure_ok and validation_ok:
        print("✅ 所有测试通过")
        print("💡 分辨率检测功能应该可以正常工作了")
        print("💡 现在1080x1920的视频应该能通过验证")
        return True
    else:
        print("❌ 部分测试失败")
        if not structure_ok:
            print("  - 元数据结构测试失败")
        if not validation_ok:
            print("  - 分辨率验证测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
