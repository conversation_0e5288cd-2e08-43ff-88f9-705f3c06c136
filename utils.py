# utils.py
import math
import os
import sys
import subprocess
import json
from typing import List


if sys.platform.startswith('win'):
    # 隐藏控制台窗口的标志
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

def sanitize_filename(filename):
    return "".join(c if c.isalnum() or c in "_-" else "_" for c in filename)

def get_video_metadata(file_path):
    try:
        # 将 ffmpeg 替换为 ffprobe
        cmd = [
            "ffprobe",
            "-i", file_path,
            "-v", "quiet",
            "-print_format", "json",
            "-show_format",
            "-show_streams"
        ]
        # 明确指定编码为 utf-8
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', creationflags=CREATE_NO_WINDOW)
        if result.returncode != 0:
            return None
        try:
            return json.loads(result.stdout)
        except json.JSONDecodeError:
            return None
    except Exception:
        return None

def get_video_duration(file_path):
    meta = get_video_metadata(file_path)
    if meta:
        return float(meta["format"]["duration"])
    return 0

def get_valid_luts(lut_folder):
    return [f for f in os.listdir(lut_folder) if f.endswith('.cube')]

def _get_video_resolution(file_path):
    meta = get_video_metadata(file_path)
    if meta:
        for stream in meta["streams"]:
            if stream["codec_type"] == "video":
                return stream["width"], stream["height"]
    return None, None

def get_process_time(stderr):
    # 解析FFmpeg的输出以获取处理时间
    pass

def determine_common_factors(n1: int, n2: int, min_k: int = 3) -> List[int]:
    """计算公共因数（原 determine_common_factors 逻辑）"""
    factors1 = set(i for i in range(min_k, n1+1) if n1 % i == 0)
    factors2 = set(i for i in range(min_k, n2+1) if n2 % i == 0)
    return sorted(factors1 & factors2, reverse=True)

def split_list(items: list, k: int) -> List[list]:
    """通用列表拆分函数（原 _split 逻辑）"""
    a, r = divmod(len(items), k)
    return [items[i*a + min(i, r): (i+1)*a + min(i+1, r)] for i in range(k)]