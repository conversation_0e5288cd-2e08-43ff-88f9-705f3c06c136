"""
简单测试洗素材功能
"""

import sys
import os
from pathlib import Path
import tempfile

# 添加当前目录到路径
sys.path.append('.')

def create_test_video():
    """创建一个测试视频文件"""
    try:
        from video_mixer import VideoMixer
        
        mixer = VideoMixer()
        ffmpeg_path = mixer.ffmpeg_path
        
        # 创建一个简单的测试视频（5秒，红色背景）
        test_video = Path("test_input.mp4")
        
        cmd = [
            str(ffmpeg_path),  # 转换为字符串
            "-f", "lavfi",
            "-i", "color=red:size=1080x1920:duration=5",
            "-c:v", "libx264",
            "-pix_fmt", "yuv420p",
            "-y",  # 覆盖输出文件
            str(test_video)
        ]
        
        print(f"创建测试视频: {' '.join(cmd)}")
        
        import subprocess
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ 测试视频创建成功: {test_video}")
            return test_video
        else:
            print(f"❌ 测试视频创建失败: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 创建测试视频失败: {str(e)}")
        return None

def test_video_cleaner_with_real_file():
    """使用真实文件测试VideoCleanerProcessor"""
    print("=== 使用真实文件测试VideoCleanerProcessor ===")
    
    try:
        # 创建测试视频
        test_video = create_test_video()
        if not test_video or not test_video.exists():
            print("❌ 无法创建测试视频")
            return False
            
        from video_cleaner import VideoCleanerProcessor
        
        # 创建输出目录
        output_dir = Path("test_wash_output")
        output_dir.mkdir(exist_ok=True)
        
        processor = VideoCleanerProcessor()
        processor.set_output_directory(str(output_dir))
        processor.set_target_resolution(1080, 1920)
        processor.set_target_bitrate(12000)
        processor.set_target_framerate(30)
        processor.replace_original = False
        
        print(f"输入文件: {test_video}")
        print(f"输出目录: {output_dir}")
        
        # 处理文件
        success = processor._process_single_file(str(test_video))
        
        if success:
            print("✅ 文件处理成功")
            
            # 检查输出文件
            output_files = list(output_dir.glob("*.mp4"))
            print(f"输出文件数量: {len(output_files)}")
            
            for file in output_files:
                file_size = file.stat().st_size
                print(f"  - {file.name} (大小: {file_size} 字节)")
                
            if output_files:
                print("✅ 找到输出文件")
                result = True
            else:
                print("❌ 没有找到输出文件")
                result = False
        else:
            print("❌ 文件处理失败")
            result = False
            
        # 清理测试文件
        try:
            if test_video.exists():
                test_video.unlink()
            if output_dir.exists():
                import shutil
                shutil.rmtree(output_dir)
        except:
            pass
            
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_autoshu_thread_with_mock():
    """使用模拟数据测试AutoWashMixingThread"""
    print("\n=== 使用模拟数据测试AutoWashMixingThread ===")
    
    try:
        # 创建测试视频
        test_video = create_test_video()
        if not test_video or not test_video.exists():
            print("❌ 无法创建测试视频")
            return False
            
        from main import AutoWashMixingThread
        from video_mixer import VideoMixer
        
        mixer = VideoMixer()
        mixer.main_files = [test_video]
        mixer.variant_files = [test_video]  # 使用同一个文件作为测试
        mixer._is_running = True
        
        # 模拟main_window
        class MockMainWindow:
            pass
        
        main_window = MockMainWindow()
        
        thread = AutoWashMixingThread(mixer, main_window)
        
        # 测试临时目录创建
        temp_dir = thread._get_temp_dir("测试临时目录")
        print(f"临时目录: {temp_dir}")
        
        if temp_dir.exists():
            print("✅ 临时目录创建成功")
            
            # 测试洗素材方法
            print("开始测试洗素材...")
            success = thread._wash_videos([test_video], temp_dir, "测试")
            
            if success:
                print("✅ 洗素材方法执行成功")
                
                # 检查洗好的文件
                washed_files = thread._get_washed_files(temp_dir)
                print(f"洗好的文件数量: {len(washed_files)}")
                
                if washed_files:
                    print("✅ 找到洗好的文件")
                    result = True
                else:
                    print("❌ 没有找到洗好的文件")
                    result = False
            else:
                print("❌ 洗素材方法执行失败")
                result = False
                
            # 清理临时目录
            try:
                import shutil
                shutil.rmtree(temp_dir)
            except:
                pass
        else:
            print("❌ 临时目录创建失败")
            result = False
            
        # 清理测试文件
        try:
            if test_video.exists():
                test_video.unlink()
        except:
            pass
            
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始简单洗素材功能测试...")
    
    results = []
    
    # 测试VideoCleanerProcessor
    results.append(test_video_cleaner_with_real_file())
    
    # 测试AutoWashMixingThread
    results.append(test_autoshu_thread_with_mock())
    
    # 汇总结果
    print("\n" + "="*50)
    print("简单测试结果汇总:")
    print(f"VideoCleanerProcessor真实文件测试: {'✅ 通过' if results[0] else '❌ 失败'}")
    print(f"AutoWashMixingThread模拟测试: {'✅ 通过' if results[1] else '❌ 失败'}")
    
    success_count = sum(results)
    total_count = len(results)
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有简单测试通过！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
