#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
授权配置生成工具
用于批量生成带时间限制的授权配置
"""

import sys
from datetime import datetime, timedelta
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                              QWidget, QLabel, QTextEdit, QPushButton, QMessageBox,
                              QGroupBox, QLineEdit, QDateEdit, QTableWidget, 
                              QTableWidgetItem, QHeaderView)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont

class AuthorizationConfigGenerator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.machine_codes = []
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("授权配置生成工具")
        self.setGeometry(200, 200, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("授权配置生成工具")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 机器码输入区域
        input_group = QGroupBox("机器码输入")
        input_layout = QVBoxLayout(input_group)
        
        input_layout.addWidget(QLabel("机器码列表（每行一个）:"))
        self.machine_code_input = QTextEdit()
        self.machine_code_input.setMaximumHeight(150)
        self.machine_code_input.setPlaceholderText("请输入机器码，每行一个...\n例如：\nb837e25899a1d51becdd9fd0bb39bec6\na1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6")
        input_layout.addWidget(self.machine_code_input)
        
        # 批量设置区域
        batch_layout = QHBoxLayout()
        
        batch_layout.addWidget(QLabel("批量过期日期:"))
        self.batch_date = QDateEdit()
        self.batch_date.setDate(QDate.currentDate().addDays(365))  # 默认1年后
        self.batch_date.setCalendarPopup(True)
        batch_layout.addWidget(self.batch_date)
        
        batch_layout.addWidget(QLabel("批量描述:"))
        self.batch_description = QLineEdit()
        self.batch_description.setPlaceholderText("例如：正式用户")
        batch_layout.addWidget(self.batch_description)
        
        generate_button = QPushButton("生成配置")
        generate_button.clicked.connect(self.generate_config)
        batch_layout.addWidget(generate_button)
        
        input_layout.addLayout(batch_layout)
        layout.addWidget(input_group)
        
        # 配置表格
        table_group = QGroupBox("授权配置")
        table_layout = QVBoxLayout(table_group)

        self.config_table = QTableWidget()
        self.config_table.setColumnCount(3)
        self.config_table.setHorizontalHeaderLabels(["机器码", "过期日期", "描述"])

        # 设置列宽
        header = self.config_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        
        table_layout.addWidget(self.config_table)
        layout.addWidget(table_group)
        
        # 输出区域
        output_group = QGroupBox("生成的代码")
        output_layout = QVBoxLayout(output_group)
        
        self.output_text = QTextEdit()
        self.output_text.setReadOnly(True)
        output_layout.addWidget(self.output_text)
        
        layout.addWidget(output_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        copy_button = QPushButton("复制代码")
        copy_button.clicked.connect(self.copy_code)
        button_layout.addWidget(copy_button)
        
        save_button = QPushButton("保存到文件")
        save_button.clicked.connect(self.save_to_file)
        button_layout.addWidget(save_button)
        
        clear_button = QPushButton("清空")
        clear_button.clicked.connect(self.clear_all)
        button_layout.addWidget(clear_button)
        
        button_layout.addStretch()
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)

    def generate_config(self):
        """生成授权配置"""
        input_text = self.machine_code_input.toPlainText().strip()
        if not input_text:
            QMessageBox.warning(self, "警告", "请输入机器码列表！")
            return
        
        machine_codes = [line.strip() for line in input_text.split('\n') if line.strip()]
        if not machine_codes:
            QMessageBox.warning(self, "警告", "没有找到有效的机器码！")
            return
        
        # 获取批量设置
        expire_date = self.batch_date.date().toString("yyyy-MM-dd")
        description = self.batch_description.text().strip() or "授权用户"
        
        # 转换机器码为MD5
        try:
            from machine_code_converter import MachineCodeConverter
            converter = MachineCodeConverter()
        except ImportError:
            QMessageBox.critical(self, "错误", "无法导入机器码转换器！\n请确保 machine_code_converter.py 文件存在。")
            return
        
        # 清空表格
        self.config_table.setRowCount(0)
        
        # 生成配置
        configs = []
        for i, machine_code in enumerate(machine_codes):
            md5_value = converter.convert_machine_code_to_md5(machine_code)
            
            # 添加到表格
            row = self.config_table.rowCount()
            self.config_table.insertRow(row)

            self.config_table.setItem(row, 0, QTableWidgetItem(machine_code))
            self.config_table.setItem(row, 1, QTableWidgetItem(expire_date))
            self.config_table.setItem(row, 2, QTableWidgetItem(f"{description}{i+1}" if len(machine_codes) > 1 else description))
            
            # 添加到配置列表
            configs.append({
                "machine_code": machine_code,
                "md5_value": md5_value,
                "expire_date": expire_date,
                "description": f"{description}{i+1}" if len(machine_codes) > 1 else description
            })
        
        # 生成代码
        self.generate_code_output(configs)

    def generate_code_output(self, configs):
        """生成代码输出"""
        code_lines = []
        code_lines.append("# 预设的授权配置 - 包含过期时间")
        code_lines.append("self.authorized_config = {")

        for config in configs:
            code_lines.append(f'    "{config["md5_value"]}": {{')
            code_lines.append(f'        "expire_date": "{config["expire_date"]}",')
            code_lines.append(f'        "description": "{config["description"]}"')
            code_lines.append("    },")

        code_lines.append("}")
        code_lines.append("")
        code_lines.append("# 机器码对应表（仅供参考）:")
        for config in configs:
            code_lines.append(f'# {config["machine_code"]} ({config["description"]})')

        self.output_text.setPlainText("\n".join(code_lines))

    def copy_code(self):
        """复制代码到剪贴板"""
        code_text = self.output_text.toPlainText()
        if code_text:
            clipboard = QApplication.clipboard()
            clipboard.setText(code_text)
            QMessageBox.information(self, "成功", "代码已复制到剪贴板！")
        else:
            QMessageBox.warning(self, "警告", "没有可复制的代码！")

    def save_to_file(self):
        """保存代码到文件"""
        code_text = self.output_text.toPlainText()
        if not code_text:
            QMessageBox.warning(self, "警告", "没有可保存的代码！")
            return
        
        try:
            filename = f"authorization_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            with open(filename, "w", encoding="utf-8") as f:
                f.write("# 授权配置文件\n")
                f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(code_text)
            QMessageBox.information(self, "成功", f"代码已保存到 {filename} 文件！")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存文件失败：{str(e)}")

    def clear_all(self):
        """清空所有内容"""
        self.machine_code_input.clear()
        self.batch_description.clear()
        self.config_table.setRowCount(0)
        self.output_text.clear()

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("授权配置生成工具")
    app.setApplicationVersion("1.0")
    
    window = AuthorizationConfigGenerator()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
