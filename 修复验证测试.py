#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复验证测试脚本
用于验证音频呲呲啦啦声音修复和转场随机选择范围修复
"""

import os
import sys
import subprocess
from pathlib import Path

def test_audio_fix():
    """测试音频处理修复"""
    print("🔍 测试一：音频处理修复验证")

    # 检查main.py中的音频处理代码
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查是否包含修复的音频处理逻辑
        fixes_found = []

        if "amix=inputs=2:duration=first:normalize=0[aout]" in content:
            fixes_found.append("✅ 背景音乐混合修复已应用")

        if "amix=inputs=2:duration=longest:normalize=0[outa]" in content:
            fixes_found.append("✅ 转场音频混合修复已应用")

        if 'volume=0.8[a0]' in content and 'amix=inputs=' in content and ':normalize=0[aout]' in content:
            fixes_found.append("✅ 多视频音频混合修复已应用")

        if '"-b:a", "256k"' in content and '"-ar", "48000"' in content:
            fixes_found.append("✅ 音频编码参数优化已应用")

        if fixes_found:
            for fix in fixes_found:
                print(fix)
            return True
        else:
            print("❌ 音频处理修复未找到")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_lk_lineEdit_3_settings():
    """测试LK_lineEdit_3设置保存功能"""
    print("\n🔍 测试二：LK_lineEdit_3设置保存功能")
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查设置保存和加载
        save_found = 'self.settings.setValue("LK_lineEdit_3", self.ui.LK_lineEdit_3.text())' in content
        load_found = 'self.ui.LK_lineEdit_3.setText(\n            self.settings.value("LK_lineEdit_3", "")' in content
        
        if save_found and load_found:
            print("✅ LK_lineEdit_3设置保存/加载功能已添加")
            return True
        else:
            print(f"❌ LK_lineEdit_3设置功能不完整 - 保存:{save_found}, 加载:{load_found}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_tab_qt_transition_config():
    """测试tab_QT转场配置调试信息"""
    print("\n🔍 测试三：tab_QT转场配置调试信息")
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查转场配置调试信息
        debug_found = "🔧 转场复选框状态:" in content and "🔧 转场类型:" in content
        
        if debug_found:
            print("✅ tab_QT转场配置调试信息已添加")
            return True
        else:
            print("❌ tab_QT转场配置调试信息未找到")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_video_mixer_transition_fix():
    """测试video_mixer.py转场时长修复"""
    print("\n🔍 测试四：video_mixer.py转场时长修复")
    
    try:
        with open('video_mixer.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查转场时长计算修复
        offset_fix = "offset = main_duration - safe_duration" in content
        duration_fix = "safe_duration = min(transition_duration, main_duration * 0.5, variant_duration * 0.5)" in content
        
        if offset_fix and duration_fix:
            print("✅ video_mixer.py转场时长计算修复已应用")
            return True
        else:
            print(f"❌ video_mixer.py转场修复不完整 - offset:{offset_fix}, duration:{duration_fix}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_transition_quotes_fix():
    """测试转场引号修复"""
    print("\n🔍 测试五：转场引号修复")
    
    try:
        # 检查main.py
        with open('main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
            
        # 检查video_mixer.py
        with open('video_mixer.py', 'r', encoding='utf-8') as f:
            mixer_content = f.read()
            
        # 检查是否使用单引号包围转场名称
        main_quotes = "transition='{transition_effect}'" in main_content
        mixer_quotes = "transition='{transition_type}'" in mixer_content
        
        if main_quotes and mixer_quotes:
            print("✅ 转场引号修复已应用到所有文件")
            return True
        else:
            print(f"❌ 转场引号修复不完整 - main.py:{main_quotes}, video_mixer.py:{mixer_quotes}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_ffmpeg_availability():
    """测试FFmpeg可用性"""
    print("\n🔍 测试六：FFmpeg可用性")
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ FFmpeg可用")
            return True
        else:
            print("❌ FFmpeg不可用")
            return False
    except Exception as e:
        print(f"❌ FFmpeg测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 视频编辑应用修复验证测试")
    print("=" * 60)
    
    tests = [
        test_tab_hj_audio_fix,
        test_lk_lineEdit_3_settings,
        test_tab_qt_transition_config,
        test_video_mixer_transition_fix,
        test_transition_quotes_fix,
        test_ffmpeg_availability
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复验证成功！")
        print("\n📝 修复总结:")
        print("✅ tab_HJ音频同步问题已修复")
        print("✅ LK_lineEdit_3设置保存功能已添加")
        print("✅ tab_QT转场配置调试信息已添加")
        print("✅ video_mixer.py转场时长计算已修复")
        print("✅ 转场引号问题已修复")
        print("✅ FFmpeg环境正常")
    else:
        print("⚠️ 部分测试未通过，请检查相关修复")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
