import os
import subprocess

def convert_ui_to_py(ui_file_path, output_path=None):
    """
    将Qt Designer的.ui文件转换为Python文件
    
    参数:
    - ui_file_path: UI文件的路径
    - output_path: 输出的Python文件路径，如果为None则自动生成
    """
    # 检查UI文件是否存在
    if not os.path.exists(ui_file_path):
        print(f"错误: 文件 '{ui_file_path}' 不存在")
        return False
    
    # 生成输出文件路径
    if output_path is None:
        directory = os.path.dirname(ui_file_path)
        output_path = os.path.join(directory, "ui_main_window.py")
    
    # 执行转换命令
    try:
        command = f"pyside6-uic {ui_file_path} -o {output_path}"
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"成功将 '{ui_file_path}' 转换为 '{output_path}'")
        return True
    except subprocess.CalledProcessError as e:
        print(f"转换失败: {e.stderr}")
        return False

if __name__ == "__main__":
    # 指定你的UI文件路径
    ui_file = r"E:\000混剪文件夹\可视化project-pyside6\MFChen视频混剪工具.ui"
    
    # 转换UI文件
    convert_ui_to_py(ui_file)