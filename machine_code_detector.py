#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器码检测工具
用于在打包前获取目标机器的唯一机器码
"""

import uuid
import hashlib
import os
import platform
import subprocess
import sys

# Windows下隐藏控制台窗口
if sys.platform.startswith('win'):
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QLabel, QPushButton, QMessageBox,
                            QGroupBox, QFrame)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QClipboard

class MachineCodeDetector:
    def __init__(self):
        self.machine_id = self._get_machine_id()

    def _get_machine_id(self):
        """生成基于多种硬件信息的稳定唯一机器码（与主程序保持一致）"""
        machine_info = []
        
        try:
            # 获取主板序列号
            if os.name == "nt":  # Windows
                try:
                    result = subprocess.run(['wmic', 'baseboard', 'get', 'serialnumber'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'SerialNumber' not in line:
                                machine_info.append(line.strip())
                except:
                    pass

                # 获取CPU序列号
                try:
                    result = subprocess.run(['wmic', 'cpu', 'get', 'processorid'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'ProcessorId' not in line:
                                machine_info.append(line.strip())
                except:
                    pass

                # 获取硬盘序列号
                try:
                    result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'],
                                          capture_output=True, text=True, timeout=10,
                                          creationflags=CREATE_NO_WINDOW)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'SerialNumber' not in line and line.strip() != '(null)':
                                machine_info.append(line.strip())
                                break  # 只取第一个硬盘
                except:
                    pass

            elif os.name == "posix":  # Linux/Mac
                try:
                    # 尝试获取机器ID
                    if os.path.exists('/etc/machine-id'):
                        with open('/etc/machine-id', 'r') as f:
                            machine_info.append(f.read().strip())
                    elif os.path.exists('/var/lib/dbus/machine-id'):
                        with open('/var/lib/dbus/machine-id', 'r') as f:
                            machine_info.append(f.read().strip())
                except:
                    pass

                # 获取CPU信息
                try:
                    with open('/proc/cpuinfo', 'r') as f:
                        for line in f:
                            if 'serial' in line.lower():
                                machine_info.append(line.split(':')[1].strip())
                                break
                except:
                    pass
        except:
            pass

        # 获取MAC地址作为备用
        try:
            mac = uuid.getnode()
            machine_info.append(str(mac))
        except:
            pass

        # 获取系统信息作为额外标识
        try:
            machine_info.append(platform.machine())
            machine_info.append(platform.processor())
        except:
            pass
        
        # 如果没有获取到任何信息，使用UUID作为备用
        if not machine_info:
            machine_info.append(str(uuid.uuid4()))
        
        # 组合所有信息并生成哈希
        combined_info = ''.join(machine_info)
        return hashlib.sha256(combined_info.encode()).hexdigest()[:32]  # 取前32位

class MachineCodeDetectorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.detector = MachineCodeDetector()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("机器码检测工具")
        self.setGeometry(300, 300, 500, 300)

        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # 标题
        title_label = QLabel("机器码检测工具")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 机器码显示区域
        machine_code_group = QGroupBox("当前机器码")
        machine_code_layout = QVBoxLayout()
        machine_code_group.setLayout(machine_code_layout)

        # 机器码标签
        self.machine_code_label = QLabel(self.detector.machine_id)
        self.machine_code_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                padding: 10px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                border-radius: 5px;
            }
        """)
        self.machine_code_label.setWordWrap(True)
        machine_code_layout.addWidget(self.machine_code_label)

        main_layout.addWidget(machine_code_group)

        # 说明文字
        info_label = QLabel("请将上述机器码发送给管理员以获取软件授权")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #666; margin: 10px;")
        main_layout.addWidget(info_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 复制按钮
        copy_button = QPushButton("复制机器码")
        copy_button.clicked.connect(self.copy_machine_code)
        copy_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(copy_button)

        # 退出按钮
        exit_button = QPushButton("退出")
        exit_button.clicked.connect(self.close)
        exit_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        button_layout.addWidget(exit_button)

        main_layout.addLayout(button_layout)

        # 添加弹性空间
        main_layout.addStretch()

    def copy_machine_code(self):
        """复制机器码到剪贴板"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.detector.machine_id)
        QMessageBox.information(self, "成功", "机器码已复制到剪贴板！")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("机器码检测工具")
    app.setApplicationVersion("1.0")
    
    window = MachineCodeDetectorGUI()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
