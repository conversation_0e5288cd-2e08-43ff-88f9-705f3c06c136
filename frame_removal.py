import os
import random
import subprocess
from pathlib import Path
from PySide6.QtCore import QObject, Signal
from utils import get_video_metadata, sanitize_filename

# 全局常量
VIDEO_BITRATE = "15000k"  # 提高码率以匹配主程序
GPU_ENABLED = True
CPU_PRESET = "slow"
GPU_PRESET = "p2"
CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

def is_gpu_supported():
    """检查NVIDIA GPU和CUDA支持"""
    try:
        # 检查nvidia-smi命令是否可用
        result = subprocess.run(
            ["nvidia-smi", "--query-gpu=name", "--format=csv,noheader"],
            capture_output=True,
            text=True,
            timeout=5,
            creationflags=CREATE_NO_WINDOW
        )
        if result.returncode == 0 and result.stdout.strip():
            return True
        else:
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, Exception):
        return False

class FrameRemovalProcessor(QObject):
    log_signal = Signal(str)  # 用于发送日志信息
    progress_signal = Signal(int, int)  # 用于发送进度信息(current, total)
    
    def __init__(self):
        super().__init__()
        self.is_running = False
        self.output_dir = ""
        self.min_frames = 15  # 默认最小抽帧数
        self.max_frames = 50  # 默认最大抽帧数
        
    def set_output_directory(self, directory):
        """设置输出目录"""
        self.output_dir = directory
        
    def set_frame_range(self, min_frames, max_frames):
        """设置抽帧范围"""
        self.min_frames = min_frames
        self.max_frames = max_frames
        
    def stop(self):
        """停止处理"""
        self.is_running = False
        
    def process_files(self, files, replace_original=False):
        """处理文件列表
        
        Args:
            files: 要处理的文件列表
            replace_original: 是否替换原文件
        """
        self.is_running = True
        processed_files = []
        total_files = len(files)
        
        for i, file in enumerate(files):
            if not self.is_running:
                break
                
            try:
                if replace_original:
                    output_path = self._process_file_with_replacement(file)
                else:
                    output_path = self._process_file_normal(file)
                    
                if output_path:
                    processed_files.append(output_path)
                    
                # 更新进度
                self.progress_signal.emit(i + 1, total_files)
                    
            except Exception as e:
                self.log_signal.emit(f"❌ 处理文件失败：{file}，错误：{str(e)[:100]}")
                
        return processed_files
    
    def _process_file_with_replacement(self, file):
        """处理文件并替换原文件"""
        try:
            # 创建临时文件路径
            file_dir = os.path.dirname(file)
            file_name = os.path.basename(file)
            base_name = os.path.splitext(file_name)[0]
            temp_output = os.path.join(file_dir, f"{base_name}_temp.mp4")

            # 先处理到临时文件
            if self._run_frame_removal(file, temp_output):
                try:
                    # 删除原文件
                    os.remove(file)
                    # 将临时文件重命名为原文件名
                    os.rename(temp_output, file)
                    self.log_signal.emit(f"✅ 替换原文件成功：{file}")
                    return file
                except Exception as e:
                    self.log_signal.emit(f"❌ 重命名临时文件失败：{str(e)[:100]}")
                    # 如果重命名失败，尝试恢复原文件
                    if os.path.exists(temp_output):
                        try:
                            os.remove(temp_output)
                        except:
                            pass
                    return None
            else:
                # 如果处理失败，清理临时文件
                if os.path.exists(temp_output):
                    try:
                        os.remove(temp_output)
                    except:
                        pass
                return None
        except Exception as e:
            self.log_signal.emit(f"❌ 替换原文件失败：{str(e)[:100]}")
            return None
            
    def _process_file_normal(self, file):
        """正常处理文件（不替换原文件）"""
        safe_name = sanitize_filename(os.path.basename(file))
        base_name = os.path.splitext(safe_name)[0]
        
        # 生成输出文件名（不添加_CZ后缀）
        output_name = f"{base_name}.mp4"
        output_path = os.path.join(self.output_dir, output_name)
        
        # 如果文件已存在，不进行处理
        if os.path.exists(output_path):
            self.log_signal.emit(f"⚠️ 文件已存在，跳过处理：{output_path}")
            return None
            
        # 处理文件
        if self._run_frame_removal(file, output_path):
            return output_path
        return None
        
    def _run_frame_removal(self, input_path, output_path):
        """执行抽帧处理"""
        # 获取视频元数据
        meta = get_video_metadata(input_path)
        if not meta:
            self.log_signal.emit(f"❌ {input_path} 元数据解析失败")
            return False
            
        # 检查帧数信息
        if 'nb_frames' not in meta.get('streams', [{}])[0]:
            self.log_signal.emit(f"❌ {input_path} 无法获取帧数信息")
            return False
            
        total_frames = int(meta['streams'][0]['nb_frames'])
        delete_frames = random.randint(self.min_frames, self.max_frames)
        
        if total_frames < delete_frames:
            self.log_signal.emit(
                f"❌ {input_path} 帧数不足（{total_frames} < {delete_frames}）"
            )
            return False
            
        # 生成时间表达式
        frame_ids = sorted(random.sample(range(total_frames), delete_frames))
        frame_duration = float(meta["format"]["duration"]) / total_frames
        time_expr = [
            f"between(t,{f*frame_duration:.6f},{(f+1)*frame_duration:.6f})"
            for f in frame_ids
        ]
        
        # 构建基础FFmpeg命令
        base_cmd = [
            "ffmpeg",
            "-i", input_path,
            "-hide_banner",
            "-vf", f"select='not({'+'.join(time_expr)})',setpts=N/FRAME_RATE/TB",
            "-af", f"aselect='not({'+'.join(time_expr)})',asetpts=N/SR/TB",
            "-c:v", "placeholder",  # 将被GPU函数替换
            "-b:v", VIDEO_BITRATE,
            "-maxrate", "18000k",
            "-bufsize", "36000k",
            "-c:a", "aac",
            "-b:a", "320k",
            "-y",
            output_path
        ]

        # 使用统一的GPU加速函数
        try:
            from main import build_gpu_ffmpeg_cmd
            cmd, using_gpu = build_gpu_ffmpeg_cmd(base_cmd)
            if using_gpu:
                self.log_signal.emit("🚀 使用GPU硬件加速处理")
            else:
                self.log_signal.emit("🔄 使用CPU处理")
        except ImportError:
            # 如果无法导入，回退到原始方法
            cmd = base_cmd
            if GPU_ENABLED and is_gpu_supported():
                cmd[cmd.index("placeholder")] = "h264_nvenc"
                cmd.insert(1, "-hwaccel")
                cmd.insert(2, "cuda")
                cmd.extend(["-preset", GPU_PRESET, "-bf", "0"])
                self.log_signal.emit("🚀 使用GPU硬件加速处理")
            else:
                cmd[cmd.index("placeholder")] = "libx264"
                cmd.extend(["-preset", CPU_PRESET])
                self.log_signal.emit("🔄 使用CPU处理")
            
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=1800,
                creationflags=CREATE_NO_WINDOW
            )
            
            if result.returncode != 0:
                self.log_signal.emit(f"❌ 失败：{result.stderr}，执行的命令：{' '.join(cmd)}")
                return False
                
            # 验证输出文件
            output_meta = get_video_metadata(output_path)
            if not output_meta:
                self.log_signal.emit(f"❌ 无法验证输出文件：{output_path}")
                return False
                
            output_frames = int(output_meta["streams"][0]["nb_frames"]) if 'nb_frames' in output_meta.get('streams', [{}])[0] else 0
            self.log_signal.emit(
                f"✅ 完成：{input_path}（原帧：{total_frames} → 新帧：{output_frames} 丨 抽帧：{total_frames - output_frames}）"
            )
            return True
            
        except subprocess.TimeoutExpired:
            self.log_signal.emit(f"❌ 失败：执行超时")
            return False
        except Exception as e:
            self.log_signal.emit(f"❌ 失败：{str(e)}")
            return False 