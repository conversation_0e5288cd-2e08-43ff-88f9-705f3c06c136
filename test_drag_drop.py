#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拖拽功能的脚本
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_drag_drop_logic():
    """测试拖拽逻辑"""
    try:
        from main import MainWindow
        from PySide6.QtWidgets import QApplication
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = MainWindow()
        
        # 测试文件路径（假设的视频文件）
        test_files = [
            "test_video1.mp4",
            "test_video2.avi",
            "test_video3.mov"
        ]
        
        print("=== 测试拖拽逻辑 ===")
        
        # 测试sy类型的拖拽处理
        print("1. 测试sy类型拖拽处理...")
        try:
            # 模拟拖拽到sy列表
            window.add_files_from_drag(test_files, "sy")
            print("✅ sy类型拖拽处理成功")
        except Exception as e:
            print(f"❌ sy类型拖拽处理失败: {e}")
        
        # 测试xsc类型的拖拽处理（对比）
        print("2. 测试xsc类型拖拽处理...")
        try:
            window.add_files_from_drag(test_files, "xsc")
            print("✅ xsc类型拖拽处理成功")
        except Exception as e:
            print(f"❌ xsc类型拖拽处理失败: {e}")
        
        # 检查sy相关属性是否存在
        print("3. 检查sy相关属性...")
        attrs_to_check = ['sy_files', 'sy_model', 'sy_list_view']
        for attr in attrs_to_check:
            if hasattr(window, attr):
                print(f"✅ 属性存在: {attr}")
            else:
                print(f"❌ 属性缺失: {attr}")
        
        # 检查方法是否存在
        print("4. 检查sy相关方法...")
        methods_to_check = ['_add_sy_files', 'add_sy_files', 'update_sy_list']
        for method in methods_to_check:
            if hasattr(window, method):
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 方法缺失: {method}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_video_file_detection():
    """测试视频文件检测"""
    try:
        from main import MainWindow
        from PySide6.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        window = MainWindow()
        
        print("=== 测试视频文件检测 ===")
        
        test_files = [
            "video.mp4",    # 应该被识别
            "video.avi",    # 应该被识别
            "video.mov",    # 应该被识别
            "image.jpg",    # 不应该被识别
            "document.txt", # 不应该被识别
            "video.MP4",    # 应该被识别（大写）
        ]
        
        for file in test_files:
            is_video = window._is_video_file(file)
            expected = file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv'))
            if is_video == expected:
                print(f"✅ {file}: {is_video}")
            else:
                print(f"❌ {file}: 期望{expected}, 实际{is_video}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 拖拽功能测试 ===")
    print()
    
    # 测试拖拽逻辑
    logic_ok = test_drag_drop_logic()
    print()
    
    # 测试视频文件检测
    detection_ok = test_video_file_detection()
    print()
    
    # 总结
    print("=== 测试结果 ===")
    if logic_ok and detection_ok:
        print("✅ 所有测试通过，拖拽功能应该可以正常使用")
        print("💡 如果界面中仍然无法拖拽，请检查：")
        print("   1. 确保拖拽的是视频文件")
        print("   2. 检查文件路径是否正确")
        print("   3. 查看控制台是否有调试信息")
        return True
    else:
        print("❌ 部分测试失败，请检查相关实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
