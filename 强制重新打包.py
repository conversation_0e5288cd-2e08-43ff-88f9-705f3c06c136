#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制重新打包，确保photo文件夹正确包含
"""

import subprocess
import sys
import os

def force_repackage():
    """强制重新打包"""
    try:
        print("=== 强制重新打包 ===")
        print("删除旧的打包文件...")
        
        # 删除旧的打包文件
        if os.path.exists('dist'):
            import shutil
            shutil.rmtree('dist')
            print("✅ 删除dist文件夹")
        
        if os.path.exists('build'):
            import shutil
            shutil.rmtree('build')
            print("✅ 删除build文件夹")
        
        # 检查photo文件夹
        print("\n检查photo文件夹...")
        if not os.path.exists('photo'):
            print("❌ photo文件夹不存在")
            return False
        
        required_files = ['HHlogo.png', 'HHlogo整张水印.png']
        for file in required_files:
            file_path = os.path.join('photo', file)
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"✅ {file} (大小: {file_size} 字节)")
            else:
                print(f"❌ {file} 不存在")
                return False
        
        others_dir = os.path.join('photo', 'others')
        if os.path.exists(others_dir):
            others_files = [f for f in os.listdir(others_dir) 
                           if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'))]
            print(f"✅ others文件夹 ({len(others_files)} 张图片)")
        else:
            print("❌ others文件夹不存在")
            return False
        
        print("\n开始打包...")
        
        # 使用命令行参数强制打包
        command = [
            'pyinstaller',
            '--onefile',
            '--windowed',
            '--clean',
            '--noconfirm',
            '--name=视频混剪工具',
        ]

        # 添加图标（如果存在）
        if os.path.exists('icon.ico'):
            command.append('--icon=icon.ico')
            print("✅ 使用图标: icon.ico")
        else:
            print("⚠️ 未找到icon.ico文件")

        # 继续添加其他参数
        command.extend([
            # 添加数据文件
            '--add-data=ui_main_window.py;.',
            '--add-data=photo;photo',
            
            # 隐藏导入
            '--hidden-import=utils',
            '--hidden-import=video_mixer',
            '--hidden-import=transition_mixer',
            '--hidden-import=frame_removal',
            '--hidden-import=machine_code_verifier',
            '--hidden-import=machine_code_detector',
            '--hidden-import=time_validator',
            '--hidden-import=machine_code_converter',
            '--hidden-import=startup_optimizer',
            '--hidden-import=authorization_config_generator',
            '--hidden-import=resource_manager',
            '--hidden-import=PySide6.QtCore',
            '--hidden-import=PySide6.QtWidgets',
            '--hidden-import=PySide6.QtGui',
            
            # 排除模块
            '--exclude-module=tkinter',
            '--exclude-module=matplotlib',
            '--exclude-module=numpy',
            '--exclude-module=scipy',
            '--exclude-module=pandas',
            
            # 输出设置
            '--distpath=dist',
            '--workpath=build',
            '--specpath=.',
            
            # 主文件
            'main.py'
        ]
        
        print("执行命令:")
        print(' '.join(command))
        print()
        
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        
        print("✅ 打包成功！")
        print("可执行文件位于: dist/视频混剪工具.exe")
        
        # 验证打包结果
        exe_path = "dist/视频混剪工具.exe"
        if os.path.exists(exe_path):
            exe_size = os.path.getsize(exe_path)
            print(f"EXE文件大小: {exe_size / (1024*1024):.1f} MB")
        
        print("\n重要提示：")
        print("1. photo文件夹已强制包含在exe中")
        print("2. 请测试exe文件的水印功能")
        print("3. 如果仍有问题，请运行'验证photo打包.py'进行诊断")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

def main():
    """主函数"""
    print("强制重新打包工具")
    print("确保photo文件夹正确包含在exe中")
    print("=" * 50)
    
    success = force_repackage()
    
    if success:
        print("\n🎉 打包完成！")
        print("请测试dist/视频混剪工具.exe中的水印功能")
    else:
        print("\n❌ 打包失败，请检查错误信息")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
