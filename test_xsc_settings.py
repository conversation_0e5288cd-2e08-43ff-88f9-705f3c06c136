"""
测试洗素材设置保存和加载功能
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QSettings

# 添加当前目录到路径
sys.path.append('.')

from main import MainWindow

def test_xsc_settings():
    """测试洗素材设置保存和加载功能"""
    app = QApplication([])
    
    print("=== 洗素材设置保存和加载测试 ===")
    print()
    
    # 1. 创建主窗口并检查默认值
    print("1. 检查默认设置:")
    window = MainWindow()
    
    default_width = window.ui.SXC_textEdit_W.toPlainText()
    default_height = window.ui.SXC_textEdit_H.toPlainText()
    default_bitrate = window.ui.SXC_textEdit_BTL.toPlainText()
    
    print(f"   - 默认宽度: {default_width}")
    print(f"   - 默认高度: {default_height}")
    print(f"   - 默认比特率: {default_bitrate}")
    print()
    
    # 2. 修改设置
    print("2. 修改设置:")
    test_width = "720"
    test_height = "1280"
    test_bitrate = "8000"
    
    window.ui.SXC_textEdit_W.setPlainText(test_width)
    window.ui.SXC_textEdit_H.setPlainText(test_height)
    window.ui.SXC_textEdit_BTL.setPlainText(test_bitrate)
    
    print(f"   - 设置宽度为: {test_width}")
    print(f"   - 设置高度为: {test_height}")
    print(f"   - 设置比特率为: {test_bitrate}")
    
    # 手动触发保存设置
    window._save_settings()
    print("   - 设置已保存")
    print()
    
    # 3. 验证设置已保存到QSettings
    print("3. 验证设置已保存:")
    settings = QSettings("YourCompany", "YourAppName")
    saved_width = settings.value("SXC_textEdit_W", "")
    saved_height = settings.value("SXC_textEdit_H", "")
    saved_bitrate = settings.value("SXC_textEdit_BTL", "")
    
    print(f"   - 保存的宽度: {saved_width}")
    print(f"   - 保存的高度: {saved_height}")
    print(f"   - 保存的比特率: {saved_bitrate}")
    
    # 验证保存是否正确
    width_ok = saved_width == test_width
    height_ok = saved_height == test_height
    bitrate_ok = saved_bitrate == test_bitrate
    
    print(f"   - 宽度保存正确: {width_ok}")
    print(f"   - 高度保存正确: {height_ok}")
    print(f"   - 比特率保存正确: {bitrate_ok}")
    print()
    
    # 4. 创建新窗口测试加载
    print("4. 测试设置加载:")
    window.close()
    window2 = MainWindow()
    
    loaded_width = window2.ui.SXC_textEdit_W.toPlainText()
    loaded_height = window2.ui.SXC_textEdit_H.toPlainText()
    loaded_bitrate = window2.ui.SXC_textEdit_BTL.toPlainText()
    
    print(f"   - 加载的宽度: {loaded_width}")
    print(f"   - 加载的高度: {loaded_height}")
    print(f"   - 加载的比特率: {loaded_bitrate}")
    
    # 验证加载是否正确
    width_load_ok = loaded_width == test_width
    height_load_ok = loaded_height == test_height
    bitrate_load_ok = loaded_bitrate == test_bitrate
    
    print(f"   - 宽度加载正确: {width_load_ok}")
    print(f"   - 高度加载正确: {height_load_ok}")
    print(f"   - 比特率加载正确: {bitrate_load_ok}")
    print()
    
    # 5. 测试自动保存功能
    print("5. 测试自动保存功能:")
    
    # 修改设置（这应该自动触发保存）
    new_width = "1920"
    new_height = "1080"
    new_bitrate = "15000"
    
    window2.ui.SXC_textEdit_W.setPlainText(new_width)
    window2.ui.SXC_textEdit_H.setPlainText(new_height)
    window2.ui.SXC_textEdit_BTL.setPlainText(new_bitrate)
    
    print(f"   - 修改宽度为: {new_width}")
    print(f"   - 修改高度为: {new_height}")
    print(f"   - 修改比特率为: {new_bitrate}")
    
    # 等待一下让信号处理完成
    app.processEvents()
    
    # 检查是否自动保存
    auto_saved_width = settings.value("SXC_textEdit_W", "")
    auto_saved_height = settings.value("SXC_textEdit_H", "")
    auto_saved_bitrate = settings.value("SXC_textEdit_BTL", "")
    
    print(f"   - 自动保存的宽度: {auto_saved_width}")
    print(f"   - 自动保存的高度: {auto_saved_height}")
    print(f"   - 自动保存的比特率: {auto_saved_bitrate}")
    
    auto_width_ok = auto_saved_width == new_width
    auto_height_ok = auto_saved_height == new_height
    auto_bitrate_ok = auto_saved_bitrate == new_bitrate
    
    print(f"   - 宽度自动保存正确: {auto_width_ok}")
    print(f"   - 高度自动保存正确: {auto_height_ok}")
    print(f"   - 比特率自动保存正确: {auto_bitrate_ok}")
    print()
    
    # 6. 总结测试结果
    print("=== 测试结果总结 ===")
    all_tests_passed = all([
        width_ok, height_ok, bitrate_ok,
        width_load_ok, height_load_ok, bitrate_load_ok,
        auto_width_ok, auto_height_ok, auto_bitrate_ok
    ])
    
    if all_tests_passed:
        print("✅ 所有测试通过！洗素材设置保存和加载功能正常工作。")
    else:
        print("❌ 部分测试失败，请检查设置保存和加载功能。")
    
    print()
    print("功能说明:")
    print("- 用户修改洗素材的宽度、高度、比特率设置后会自动保存")
    print("- 下次启动程序时会自动加载上次的设置")
    print("- 如果没有保存的设置，会使用默认值（1080x1920，12000kbps）")
    
    window2.close()
    
    # 设置定时器关闭应用
    QTimer.singleShot(1000, app.quit)
    
    return app.exec()

if __name__ == "__main__":
    test_xsc_settings()
