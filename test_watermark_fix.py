#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试水印处理修复
"""

import os
import sys
import subprocess
from utils import get_video_metadata

def test_metadata_parsing():
    """测试视频元数据解析"""
    print("=== 测试视频元数据解析 ===")
    
    # 测试一个不存在的文件
    print("测试不存在的文件...")
    metadata = get_video_metadata("nonexistent.mp4")
    print(f"不存在文件的元数据: {metadata}")
    
    # 测试空的metadata
    print("\n测试空metadata处理...")
    test_metadata = None
    if test_metadata and 'streams' in test_metadata and len(test_metadata['streams']) > 0:
        print("有streams")
    else:
        print("无streams或streams为空")
    
    # 测试空streams
    print("\n测试空streams处理...")
    test_metadata = {'streams': []}
    if test_metadata and 'streams' in test_metadata and len(test_metadata['streams']) > 0:
        print("有streams")
    else:
        print("无streams或streams为空")
    
    # 测试正常metadata
    print("\n测试正常metadata处理...")
    test_metadata = {
        'streams': [
            {
                'width': 1920,
                'height': 1080,
                'nb_frames': 1000,
                'r_frame_rate': '30/1'
            }
        ]
    }
    if test_metadata and 'streams' in test_metadata and len(test_metadata['streams']) > 0:
        video_stream = test_metadata['streams'][0]
        width = video_stream.get('width', 0)
        height = video_stream.get('height', 0)
        print(f"正常metadata: {width}x{height}")
    else:
        print("无streams或streams为空")

def test_ffmpeg_encoding():
    """测试FFmpeg编码处理"""
    print("\n=== 测试FFmpeg编码处理 ===")
    
    ffmpeg_path = "D:\\FFmpeg\\ffmpeg-2025-03-31-git-35c091f4b7-full_build\\bin\\ffmpeg.exe"
    if not os.path.exists(ffmpeg_path):
        print("FFmpeg不存在")
        return
    
    # 测试FFmpeg版本命令（可能包含特殊字符）
    cmd = [ffmpeg_path, "-version"]
    
    print("测试text=True模式...")
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=5
        )
        print(f"text=True成功: {result.returncode}")
    except Exception as e:
        print(f"text=True失败: {str(e)}")
    
    print("测试text=False模式...")
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=False,
            timeout=5
        )
        print(f"text=False成功: {result.returncode}")
        if result.stdout:
            try:
                stdout_text = result.stdout.decode('utf-8', errors='ignore')
                print(f"输出长度: {len(stdout_text)}")
            except Exception as e:
                print(f"解码失败: {str(e)}")
    except Exception as e:
        print(f"text=False失败: {str(e)}")

if __name__ == "__main__":
    test_metadata_parsing()
    test_ffmpeg_encoding()
    print("\n=== 测试完成 ===")
