#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
tab_HJ视频合成测试脚本 - 模拟实际的合成过程
"""

import subprocess
import os
import tempfile
from pathlib import Path

# Windows下隐藏控制台窗口
CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

# 配置参数
VIDEO_BITRATE = "15000k"
GPU_ENABLED = True
CPU_PRESET = "slow"
GPU_PRESET = "p2"

def create_test_video(output_path, duration=5, color="red", width=1920, height=1080):
    """创建测试视频"""
    ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    
    cmd = [
        str(ffmpeg_path),
        "-f", "lavfi",
        "-i", f"testsrc2=duration={duration}:size={width}x{height}:rate=30",
        "-f", "lavfi", 
        "-i", f"sine=frequency=1000:duration={duration}",
        "-c:v", "libx264",
        "-c:a", "aac",
        "-t", str(duration),
        "-y",
        str(output_path)
    ]
    
    print(f"🎬 创建测试视频: {output_path}")
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        creationflags=CREATE_NO_WINDOW
    )
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"✅ 测试视频创建成功: {os.path.getsize(output_path)} bytes")
        return True
    else:
        print(f"❌ 测试视频创建失败: {result.stderr[:200]}")
        return False

def test_simple_concat(video1, video2, output_path):
    """测试简单拼接"""
    print(f"\n🔍 测试简单拼接...")
    
    ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    
    # 创建concat文件
    concat_file = Path(output_path).parent / "test_concat.txt"
    with open(concat_file, 'w', encoding='utf-8') as f:
        f.write(f"file '{Path(video1).absolute()}'\n")
        f.write(f"file '{Path(video2).absolute()}'\n")
    
    cmd = [
        str(ffmpeg_path),
        "-f", "concat",
        "-safe", "0",
        "-i", str(concat_file),
        "-c", "copy",
        "-y",
        str(output_path)
    ]
    
    print(f"🔧 拼接命令: {' '.join(cmd[:8])}...")
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        timeout=60,
        creationflags=CREATE_NO_WINDOW
    )
    
    # 清理临时文件
    try:
        concat_file.unlink()
    except:
        pass
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"✅ 简单拼接成功: {os.path.getsize(output_path)} bytes")
        return True
    else:
        print(f"❌ 简单拼接失败 (返回码: {result.returncode})")
        print(f"错误详情: {result.stderr[:300]}")
        return False

def test_transition_merge(video1, video2, output_path):
    """测试转场合并"""
    print(f"\n🔍 测试转场合并...")
    
    ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    
    # 简化的转场滤镜
    filter_complex = "[0:v][1:v]xfade=transition='fade':duration=1.0:offset=4.0[outv]"
    
    cmd = [
        str(ffmpeg_path),
        "-i", str(video1),
        "-i", str(video2),
        "-filter_complex", filter_complex,
        "-map", "[outv]",
        "-map", "0:a",
        "-c:v", "libx264",
        "-c:a", "aac",
        "-b:v", VIDEO_BITRATE,
        "-preset", CPU_PRESET,
        "-y",
        str(output_path)
    ]
    
    print(f"🔧 转场命令: {' '.join(cmd[:8])}...")
    print(f"🎬 滤镜: {filter_complex}")
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        timeout=120,
        creationflags=CREATE_NO_WINDOW
    )
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"✅ 转场合并成功: {os.path.getsize(output_path)} bytes")
        return True
    else:
        print(f"❌ 转场合并失败 (返回码: {result.returncode})")
        print(f"错误详情: {result.stderr[:500]}")
        return False

def test_gpu_encoding(video1, video2, output_path):
    """测试GPU编码"""
    print(f"\n🔍 测试GPU编码...")
    
    ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    
    # GPU编码的转场滤镜
    filter_complex = "[0:v][1:v]xfade=transition='fade':duration=1.0:offset=4.0[outv]"
    
    cmd = [
        str(ffmpeg_path),
        "-hwaccel", "cuda",
        "-i", str(video1),
        "-i", str(video2),
        "-filter_complex", filter_complex,
        "-map", "[outv]",
        "-map", "0:a",
        "-c:v", "h264_nvenc",
        "-c:a", "aac",
        "-b:v", VIDEO_BITRATE,
        "-preset", GPU_PRESET,
        "-bf", "0",
        "-y",
        str(output_path)
    ]
    
    print(f"🔧 GPU命令: {' '.join(cmd[:10])}...")
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        timeout=120,
        creationflags=CREATE_NO_WINDOW
    )
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"✅ GPU编码成功: {os.path.getsize(output_path)} bytes")
        return True
    else:
        print(f"❌ GPU编码失败 (返回码: {result.returncode})")
        print(f"错误详情: {result.stderr[:500]}")
        return False

def main():
    """主测试函数"""
    print("🎬 tab_HJ视频合成测试")
    print("=" * 50)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试视频
        video1 = temp_path / "test_video1.mp4"
        video2 = temp_path / "test_video2.mp4"
        
        print("🎬 创建测试视频...")
        if not create_test_video(video1, duration=5, color="red"):
            print("❌ 无法创建测试视频1")
            return
        
        if not create_test_video(video2, duration=5, color="blue"):
            print("❌ 无法创建测试视频2")
            return
        
        # 测试1: 简单拼接
        output1 = temp_path / "output_concat.mp4"
        success1 = test_simple_concat(video1, video2, output1)
        
        # 测试2: CPU转场合并
        output2 = temp_path / "output_transition_cpu.mp4"
        success2 = test_transition_merge(video1, video2, output2)
        
        # 测试3: GPU转场合并
        output3 = temp_path / "output_transition_gpu.mp4"
        success3 = test_gpu_encoding(video1, video2, output3)
        
        # 汇总结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        print(f"  简单拼接: {'✅ 成功' if success1 else '❌ 失败'}")
        print(f"  CPU转场: {'✅ 成功' if success2 else '❌ 失败'}")
        print(f"  GPU转场: {'✅ 成功' if success3 else '❌ 失败'}")
        
        if not any([success1, success2, success3]):
            print("\n❌ 所有测试都失败了，可能存在系统级问题")
        elif success1 and not success2:
            print("\n🔍 简单拼接成功但转场失败，可能是滤镜问题")
        elif success2 and not success3:
            print("\n🔍 CPU转场成功但GPU失败，可能是GPU驱动问题")
        else:
            print("\n✅ 基本功能正常，问题可能在具体的实现细节")

if __name__ == "__main__":
    main()
