#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FFmpeg测试脚本 - 用于诊断tab_HJ的视频合成问题
"""

import subprocess
import os
from pathlib import Path

# Windows下隐藏控制台窗口
CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

def test_ffmpeg_paths():
    """测试FFmpeg路径"""
    print("🔍 测试FFmpeg路径...")
    
    ffmpeg_paths = [
        Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
        Path("C:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
        Path("E:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
        Path("F:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    ]
    
    for path in ffmpeg_paths:
        exists = path.exists()
        print(f"  {path}: {'✅ 存在' if exists else '❌ 不存在'}")
        if exists:
            return path
    
    print("❌ 未找到FFmpeg")
    return None

def test_ffmpeg_version(ffmpeg_path):
    """测试FFmpeg版本"""
    print(f"\n🔍 测试FFmpeg版本: {ffmpeg_path}")
    
    try:
        cmd = [str(ffmpeg_path), "-version"]
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=10,
            creationflags=CREATE_NO_WINDOW
        )
        
        if result.returncode == 0:
            version_info = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg版本: {version_info}")
            return True
        else:
            print(f"❌ FFmpeg版本检查失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ FFmpeg版本检查异常: {str(e)}")
        return False

def test_simple_command(ffmpeg_path):
    """测试简单的FFmpeg命令"""
    print(f"\n🔍 测试简单FFmpeg命令...")
    
    try:
        # 测试最简单的命令：显示帮助
        cmd = [str(ffmpeg_path), "-h"]
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=10,
            creationflags=CREATE_NO_WINDOW
        )
        
        if result.returncode == 0:
            print("✅ 简单命令执行成功")
            return True
        else:
            print(f"❌ 简单命令执行失败: {result.stderr[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 简单命令执行异常: {str(e)}")
        return False

def test_gpu_support():
    """测试GPU支持"""
    print(f"\n🔍 测试GPU支持...")
    
    try:
        # 检查nvidia-smi命令是否可用
        result = subprocess.run(
            ["nvidia-smi", "--query-gpu=name", "--format=csv,noheader"],
            capture_output=True,
            text=True,
            timeout=5,
            creationflags=CREATE_NO_WINDOW
        )
        if result.returncode == 0 and result.stdout.strip():
            gpu_name = result.stdout.strip()
            print(f"✅ 检测到GPU: {gpu_name}")
            return True
        else:
            print("❌ 未检测到NVIDIA GPU")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, Exception) as e:
        print(f"❌ GPU检测失败: {str(e)}")
        return False

def test_video_files():
    """检查是否有测试视频文件"""
    print(f"\n🔍 检查测试视频文件...")
    
    # 检查常见的视频文件位置
    test_dirs = [
        ".",
        "test_videos",
        "../test_videos",
        "素材"
    ]
    
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
    found_videos = []
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for file in os.listdir(test_dir):
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    video_path = os.path.join(test_dir, file)
                    if os.path.getsize(video_path) > 1024:  # 至少1KB
                        found_videos.append(video_path)
                        print(f"✅ 找到视频文件: {video_path}")
    
    if not found_videos:
        print("❌ 未找到测试视频文件")
    
    return found_videos

def main():
    """主测试函数"""
    print("🎬 tab_HJ FFmpeg诊断测试")
    print("=" * 50)
    
    # 1. 测试FFmpeg路径
    ffmpeg_path = test_ffmpeg_paths()
    if not ffmpeg_path:
        print("\n❌ 无法找到FFmpeg，请检查安装路径")
        return
    
    # 2. 测试FFmpeg版本
    if not test_ffmpeg_version(ffmpeg_path):
        print("\n❌ FFmpeg版本检查失败")
        return
    
    # 3. 测试简单命令
    if not test_simple_command(ffmpeg_path):
        print("\n❌ FFmpeg基本功能测试失败")
        return
    
    # 4. 测试GPU支持
    gpu_supported = test_gpu_support()
    
    # 5. 检查视频文件
    video_files = test_video_files()
    
    print("\n" + "=" * 50)
    print("📊 诊断结果汇总:")
    print(f"  FFmpeg路径: ✅ {ffmpeg_path}")
    print(f"  FFmpeg功能: ✅ 正常")
    print(f"  GPU支持: {'✅ 支持' if gpu_supported else '❌ 不支持'}")
    print(f"  测试视频: {'✅ 找到' if video_files else '❌ 未找到'} ({len(video_files)}个)")
    
    if video_files and len(video_files) >= 2:
        print(f"\n🎯 建议使用以下视频进行测试:")
        for i, video in enumerate(video_files[:2]):
            print(f"  视频{i+1}: {video}")

if __name__ == "__main__":
    main()
