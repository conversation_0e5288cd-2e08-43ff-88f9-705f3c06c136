#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示续期功能
展示如何为用户生成多个授权码实现续期，每个授权码最长7天
"""

import sys
from datetime import datetime, timedelta

def demo_user_renewal_scenario():
    """演示用户续期场景"""
    print("=" * 60)
    print("用户续期场景演示")
    print("=" * 60)
    print("场景：用户A需要使用软件15天，但单个授权码最长只能7天")
    print("解决方案：生成多个授权码进行续期")
    print("=" * 60)
    
    try:
        from portable_auth_generator import PortableAuthGenerator
        from portable_auth_verifier import PortableAuthVerifier
        
        generator = PortableAuthGenerator()
        verifier = PortableAuthVerifier("演示应用")
        
        user_id = "用户A"
        base_date = datetime.now().date()
        
        # 第一期：当前日期开始，7天有效期
        print("第一期授权码生成：")
        start_date_1 = base_date
        end_date_1 = start_date_1 + timedelta(days=7)
        
        code_1 = generator.generate_portable_auth_code(start_date_1, end_date_1, user_id)
        print(f"  有效期: {start_date_1} 至 {end_date_1}")
        print(f"  授权码: {code_1}")
        
        # 验证第一期授权码
        valid_1, _, message_1 = verifier.verify_portable_auth_code(code_1)
        print(f"  验证结果: {message_1}")
        
        # 第二期：第一期结束后开始，7天有效期
        print("\n第二期授权码生成（续期）：")
        start_date_2 = end_date_1 + timedelta(days=1)  # 第一期结束后的第二天开始
        end_date_2 = start_date_2 + timedelta(days=7)
        
        code_2 = generator.generate_portable_auth_code(start_date_2, end_date_2, user_id)
        print(f"  有效期: {start_date_2} 至 {end_date_2}")
        print(f"  授权码: {code_2}")
        
        # 验证第二期授权码（目前应该尚未生效）
        valid_2, _, message_2 = verifier.verify_portable_auth_code(code_2)
        print(f"  验证结果: {message_2}")
        
        # 第三期：第二期结束后开始，1天有效期（完成15天总期限）
        print("\n第三期授权码生成（最后续期）：")
        start_date_3 = end_date_2 + timedelta(days=1)
        end_date_3 = start_date_3 + timedelta(days=1)  # 只需要1天完成总共15天
        
        code_3 = generator.generate_portable_auth_code(start_date_3, end_date_3, user_id)
        print(f"  有效期: {start_date_3} 至 {end_date_3}")
        print(f"  授权码: {code_3}")
        
        # 验证第三期授权码
        valid_3, _, message_3 = verifier.verify_portable_auth_code(code_3)
        print(f"  验证结果: {message_3}")
        
        # 显示总的使用期限
        total_days = (end_date_3 - start_date_1).days
        print(f"\n📊 总使用期限: {total_days} 天 ({start_date_1} 至 {end_date_3})")
        print("✅ 通过3个授权码实现了15天的使用期限")
        print("✅ 每个授权码都不超过7天限制")
        
        return [code_1, code_2, code_3]
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return []

def demo_overlapping_codes():
    """演示重叠授权码的处理"""
    print("\n" + "=" * 60)
    print("重叠授权码处理演示")
    print("=" * 60)
    print("场景：管理员给用户生成了重叠时间的授权码")
    print("=" * 60)
    
    try:
        from portable_auth_generator import PortableAuthGenerator
        from portable_auth_verifier import PortableAuthVerifier
        
        generator = PortableAuthGenerator()
        verifier = PortableAuthVerifier("重叠测试应用")
        
        user_id = "用户B"
        base_date = datetime.now().date()
        
        # 第一个授权码：7天
        print("第一个授权码：")
        start_date_1 = base_date
        end_date_1 = start_date_1 + timedelta(days=7)
        
        code_1 = generator.generate_portable_auth_code(start_date_1, end_date_1, user_id)
        print(f"  有效期: {start_date_1} 至 {end_date_1}")
        
        # 第二个授权码：从第5天开始，7天（与第一个重叠）
        print("\n第二个授权码（与第一个重叠）：")
        start_date_2 = base_date + timedelta(days=4)  # 第5天开始
        end_date_2 = start_date_2 + timedelta(days=7)
        
        code_2 = generator.generate_portable_auth_code(start_date_2, end_date_2, user_id)
        print(f"  有效期: {start_date_2} 至 {end_date_2}")
        
        # 验证两个授权码
        print("\n验证结果：")
        valid_1, _, message_1 = verifier.verify_portable_auth_code(code_1)
        print(f"  第一个授权码: {message_1}")
        
        valid_2, _, message_2 = verifier.verify_portable_auth_code(code_2)
        print(f"  第二个授权码: {message_2}")
        
        # 显示有效授权码
        active_auths = verifier.get_active_portable_auths()
        print(f"\n当前有效授权码数量: {len(active_auths)}")
        for i, auth in enumerate(active_auths, 1):
            print(f"  授权码{i}: {auth['start_date']} 至 {auth['end_date']}")
        
        print("✅ 系统正确处理了重叠的授权码")
        print("✅ 用户可以在重叠期间正常使用软件")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

def demo_anti_crack_protection():
    """演示防破解保护"""
    print("\n" + "=" * 60)
    print("防破解保护演示")
    print("=" * 60)
    print("场景：检测超过7天的授权码（可能被破解）")
    print("=" * 60)
    
    try:
        from portable_auth_verifier import PortableAuthVerifier
        import json
        import base64
        import hmac
        import hashlib
        
        verifier = PortableAuthVerifier("防破解测试应用")
        
        # 模拟构造一个30天的授权码（绕过生成器检查）
        print("模拟破解场景：构造30天授权码")
        
        start_date = datetime.now().date()
        end_date = start_date + timedelta(days=30)
        
        auth_data = {
            "start": start_date.strftime("%Y-%m-%d"),
            "end": end_date.strftime("%Y-%m-%d"),
            "user": "破解者",
            "version": "1.0"
        }
        
        # 使用正确的密钥生成签名（模拟破解者获得了密钥）
        secret_key = "MFChen_Portable_Auth_2024_Secret_Key"
        data_str = json.dumps(auth_data, sort_keys=True, separators=(',', ':'))
        signature = hmac.new(secret_key.encode(), data_str.encode(), hashlib.sha256).hexdigest()[:16]
        
        final_data = {
            "data": auth_data,
            "sig": signature
        }
        
        json_str = json.dumps(final_data, separators=(',', ':'))
        encoded = base64.b64encode(json_str.encode()).decode()
        fake_code = f"PAC_{encoded}"
        
        print(f"  构造的30天授权码: {fake_code[:50]}...")
        print(f"  有效期: {start_date} 至 {end_date} (30天)")
        
        # 尝试验证这个授权码
        print("\n验证结果：")
        valid, auth_data, message = verifier.verify_portable_auth_code(fake_code)
        
        if valid:
            print(f"❌ 危险：30天授权码被接受了！")
            return False
        else:
            print(f"✅ 安全：30天授权码被正确拒绝")
            print(f"  拒绝原因: {message}")
        
        print("\n🛡️ 防破解保护正常工作：")
        print("  - 超过7天的授权码会被自动拒绝")
        print("  - 即使签名正确也无法绕过7天限制")
        print("  - 有效防止了授权码被恶意修改")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

def main():
    """主演示函数"""
    print("便携授权码续期功能演示")
    print("=" * 60)
    print("展示如何正确使用7天限制实现长期授权")
    print("=" * 60)
    
    results = []
    
    # 演示用户续期场景
    renewal_codes = demo_user_renewal_scenario()
    results.append(len(renewal_codes) == 3)
    
    # 演示重叠授权码处理
    results.append(demo_overlapping_codes())
    
    # 演示防破解保护
    results.append(demo_anti_crack_protection())
    
    # 总结
    print("\n" + "=" * 60)
    print("演示总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"演示通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 续期功能演示完成！")
        print("\n📋 功能特点确认：")
        print("✅ 单个授权码最长7天（防破解）")
        print("✅ 支持多个授权码续期实现长期使用")
        print("✅ 支持重叠时间的授权码")
        print("✅ 自动拒绝超过7天的授权码")
        print("✅ 用户体验流畅，感觉不到7天限制")
        
        print("\n📖 使用建议：")
        print("1. 为短期用户（1-7天）：生成单个授权码")
        print("2. 为长期用户（>7天）：生成多个连续的授权码")
        print("3. 可以提前生成续期授权码，用户按需使用")
        print("4. 重叠时间的授权码可以确保无缝衔接")
        
        return 0
    else:
        print("⚠️ 部分演示失败，需要检查实现")
        return 1

if __name__ == "__main__":
    sys.exit(main())
