#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包准备测试脚本
验证所有修改是否正确，确保可以正常打包
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_resource_manager():
    """测试资源管理模块"""
    print("=== 测试资源管理模块 ===")
    
    try:
        from resource_manager import (
            get_photo_path, 
            get_photo_folder_path, 
            validate_photo_structure,
            check_photo_resources
        )
        
        print("✅ 资源管理模块导入成功")
        
        # 测试路径获取
        logo_path = get_photo_path("HHlogo.png")
        watermark_path = get_photo_path("HHlogo整张水印.png")
        others_path = get_photo_folder_path("others")
        
        print(f"✅ Logo路径: {logo_path}")
        print(f"✅ 水印路径: {watermark_path}")
        print(f"✅ Others路径: {others_path}")
        
        # 验证资源结构
        is_valid, missing, message = validate_photo_structure()
        if is_valid:
            print("✅ Photo资源结构完整")
        else:
            print(f"❌ Photo资源结构问题: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 资源管理模块测试失败: {e}")
        return False

def test_main_imports():
    """测试主程序导入"""
    print("\n=== 测试主程序导入 ===")
    
    try:
        # 测试主要模块导入
        from main import MainWindow
        print("✅ MainWindow导入成功")
        
        from ui_main_window import Ui_mainWindow
        print("✅ UI模块导入成功")
        
        from utils import get_video_metadata, sanitize_filename
        print("✅ 工具模块导入成功")
        
        from video_mixer import VideoMixer
        print("✅ 视频混剪模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 主程序导入失败: {e}")
        return False

def test_watermark_paths():
    """测试水印路径修改"""
    print("\n=== 测试水印路径修改 ===")
    
    try:
        from resource_manager import get_photo_path, get_photo_folder_path
        
        # 检查关键路径
        paths_to_check = [
            ("HHlogo.png", get_photo_path("HHlogo.png")),
            ("HHlogo整张水印.png", get_photo_path("HHlogo整张水印.png")),
            ("others文件夹", get_photo_folder_path("others"))
        ]
        
        all_exist = True
        for name, path in paths_to_check:
            if os.path.exists(path):
                print(f"✅ {name}: {path}")
            else:
                print(f"❌ {name}: {path} (不存在)")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ 路径测试失败: {e}")
        return False

def test_build_config():
    """测试打包配置"""
    print("\n=== 测试打包配置 ===")
    
    try:
        from build_config import check_requirements, get_pyinstaller_command
        
        print("✅ 打包配置模块导入成功")
        
        # 检查打包要求
        requirements_ok = check_requirements()
        
        if requirements_ok:
            print("✅ 打包要求检查通过")
        else:
            print("❌ 打包要求检查失败")
            return False
        
        # 生成打包命令
        cmd = get_pyinstaller_command()
        print(f"✅ 打包命令生成成功 ({len(cmd)} 个参数)")
        
        return True
        
    except Exception as e:
        print(f"❌ 打包配置测试失败: {e}")
        return False

def test_photo_structure():
    """详细测试photo文件夹结构"""
    print("\n=== 详细测试photo文件夹结构 ===")
    
    try:
        from resource_manager import list_photo_resources, get_others_images
        
        # 列出所有资源
        resources = list_photo_resources()
        
        if not resources["exists"]:
            print("❌ photo文件夹不存在")
            return False
        
        print(f"✅ photo文件夹: {resources['path']}")
        print(f"  文件数量: {len(resources['files'])}")
        print(f"  子文件夹数量: {len(resources['folders'])}")
        
        # 检查必需文件
        required_files = ["HHlogo.png", "HHlogo整张水印.png"]
        for file in required_files:
            if file in resources["files"]:
                print(f"  ✅ {file}")
            else:
                print(f"  ❌ {file} (缺失)")
                return False
        
        # 检查others文件夹
        if "others" in resources["folders"]:
            print(f"  ✅ others文件夹")
            
            # 检查others中的图片
            others_images = get_others_images()
            print(f"    图片数量: {len(others_images)}")
            
            if len(others_images) == 0:
                print("    ❌ others文件夹中没有图片")
                return False
            else:
                print(f"    ✅ 找到 {len(others_images)} 张图片")
        else:
            print(f"  ❌ others文件夹 (缺失)")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ photo结构测试失败: {e}")
        return False

def test_pyinstaller_availability():
    """测试PyInstaller可用性"""
    print("\n=== 测试PyInstaller可用性 ===")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
        
        # 检查关键模块
        from PyInstaller import __main__
        print("✅ PyInstaller主模块可用")
        
        return True
        
    except ImportError:
        print("❌ PyInstaller未安装")
        print("请运行: pip install pyinstaller")
        return False
    except Exception as e:
        print(f"❌ PyInstaller测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== MFChen视频混剪工具 打包准备测试 ===")
    print("验证所有修改是否正确，确保可以正常打包")
    print()
    
    # 执行所有测试
    tests = [
        ("资源管理模块", test_resource_manager),
        ("主程序导入", test_main_imports),
        ("水印路径修改", test_watermark_paths),
        ("photo文件夹结构", test_photo_structure),
        ("打包配置", test_build_config),
        ("PyInstaller可用性", test_pyinstaller_availability),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！")
        print("✅ 可以开始打包流程")
        print("\n📋 打包步骤:")
        print("1. 运行 build.bat (Windows)")
        print("2. 或运行 python build_config.py 查看详细命令")
        print("3. 打包完成后测试 dist/MFChen视频混剪工具.exe")
        return True
    else:
        print("❌ 部分测试失败，请解决问题后重试")
        failed_count = total_tests - passed_tests
        print(f"需要解决 {failed_count} 个问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
