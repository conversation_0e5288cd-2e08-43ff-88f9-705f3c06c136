#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试授权对话框（包含便携授权码功能）
"""

import sys
import os

def test_auth_dialog():
    """测试授权对话框"""
    try:
        from machine_code_verifier import MachineCodeVerifier, AuthorizationDialog
        from PySide6.QtWidgets import QApplication
        
        # 创建一个临时的未授权验证器
        class TestMachineCodeVerifier(MachineCodeVerifier):
            def __init__(self, app_name="TestApp"):
                super().__init__(app_name)
                # 清空授权配置以模拟未授权状态
                self.authorized_config = {}
        
        app = QApplication(sys.argv)
        
        # 创建未授权的验证器
        verifier = TestMachineCodeVerifier("测试应用")
        
        # 创建授权对话框
        auth_dialog = AuthorizationDialog(verifier, "测试应用")
        auth_dialog.show()
        
        print("授权对话框已打开，您可以:")
        print("1. 查看当前机器码")
        print("2. 输入便携授权码进行验证")
        print("3. 测试便携授权码功能")
        print("\n便携授权码示例（3天有效期）:")
        
        # 生成一个测试用的便携授权码
        try:
            from portable_auth_generator import PortableAuthGenerator
            from datetime import datetime, timedelta
            
            generator = PortableAuthGenerator()
            start_date = datetime.now().date()
            end_date = start_date + timedelta(days=3)
            
            test_auth_code = generator.generate_portable_auth_code(start_date, end_date, "测试用户")
            print(f"{test_auth_code}")
            print(f"\n有效期: {start_date} 至 {end_date}")
            
        except Exception as e:
            print(f"生成测试授权码失败: {e}")
        
        return app.exec()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_auth_dialog())
