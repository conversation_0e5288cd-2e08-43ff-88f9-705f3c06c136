import os
import subprocess
import json
import random
from collections import defaultdict

# 编码参数配置
VIDEO_BITRATE = "15000k"  # 提高到15M，与主程序保持一致
GPU_ENABLED = True
CPU_PRESET = "slow"
GPU_PRESET = "p2"

def list_video_files(directory):
    """列出目录中所有视频文件"""
    video_exts = ('.mp4', '.avi', '.mkv', '.mov')
    return [os.path.join(directory, f) for f in os.listdir(directory) if f.lower().endswith(video_exts)]

def get_video_info(video_path):
    """获取视频的分辨率、帧率和时长"""
    cmd = [
        'ffprobe', '-v', 'error',
        '-show_entries', 'format=duration:stream=width,height,r_frame_rate',
        '-of', 'json',
        video_path
    ]
    try:
        output = subprocess.check_output(cmd, stderr=subprocess.STDOUT, encoding='utf-8')
        data = json.loads(output)
        stream = data['streams'][0]
        format_info = data['format']
        return {
            'duration': float(format_info['duration']),
            'width': stream['width'],
            'height': stream['height'],
            'fps': float(eval(stream['r_frame_rate']))  # 解析分数形式的帧率
        }
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return {'duration': 0, 'width': 0, 'height': 0, 'fps': 0}

def filter_compatible_videos(videos):
    """筛选分辨率和帧率一致的视频"""
    video_features = []
    for path in videos:
        info = get_video_info(path)
        if all(info.values()):  # 确保所有关键信息都有效
            video_features.append((path, info))

    if not video_features:
        return [], "所有视频均无法获取有效分辨率/帧率信息"

    # 按分辨率和帧率分组
    groups = defaultdict(list)
    for path, info in video_features:
        key = (info['width'], info['height'], info['fps'])
        groups[key].append(path)

    # 选择最大的兼容组
    compatible_group = max(groups.values(), key=len, default=[])
    if not compatible_group:
        return [], "所有视频的分辨率/帧率均不一致"

    # 输出被剔除的视频
    excluded = [path for path, _ in video_features if path not in compatible_group]
    if excluded:
        print(f"已剔除 {len(excluded)} 个不兼容视频")

    return compatible_group, video_features[0][1]  # 返回兼容视频列表和公共参数

def parse_transition_type(transition_text):
    """解析转场类型文本，返回FFmpeg转场效果名称"""
    # 完整的转场映射表，包含所有42个选项
    transition_map = {
        "【【全随机】】": "random_all",
        "【【柔 · 随机】】": "random_soft",
        "【【硬 · 随机】】": "random_hard",
        "【柔】【叠化】fade": "fade",
        "【向左擦除】wipeleft": "wipeleft",
        "【向右擦除】wiperight": "wiperight",
        "【向上擦除】wipeup": "wipeup",
        "【向下擦除】wipedown": "wipedown",
        "【柔】【向左擦除】smoothleft": "smoothleft",
        "【柔】【向右擦除】smoothright": "smoothright",
        "【柔】【向上擦除】smoothup": "smoothup",
        "【柔】【向下擦除】smoothdown": "smoothdown",
        "【向左滑动】slideleft": "slideleft",
        "【向右滑动】slideright": "slideright",
        "【向上滑动】slideup": "slideup",
        "【向下滑动】slidedown": "slidedown",
        "【柔】【圆形展开】circleopen": "circleopen",
        "【柔】【圆形闭合】circleclose": "circleclose",
        "【柔】【垂直展开】vertopen": "vertopen",
        "【柔】【垂直闭合】vertclose": "vertclose",
        "【柔】【水平展开】horzopen": "horzopen",
        "【柔】【水平闭合】horzclose": "horzclose",
        "【柔】【景深转场】distance": "distance",
        "【时钟擦除】radial": "radial",
        "【像素模糊】pixelize": "pixelize",
        "【放大转场】zoomin": "zoomin",
        "【柔】【向左上擦除】diagtl": "diagtl",
        "【柔】【向右上擦除】diagtr": "diagtr",
        "【柔】【向左下擦除】diagbl": "diagbl",
        "【柔】【向右下擦除】diagbr": "diagbr",
        "【向左上擦除】wipetl": "wipetl",
        "【向右上擦除】wipetr": "wipetr",
        "【向左下擦除】wipebl": "wipebl",
        "【向右下擦除】wipebr": "wipebr",
        "【向左百叶窗】hlslice": "hlslice",
        "【向右百叶窗】hrslice": "hrslice",
        "【向上百叶窗】vuslice": "vuslice",
        "【向下百叶窗】vdslice": "vdslice",
        "【向左滑刺】hlwind": "hlwind",
        "【向右滑刺】hrwind": "hrwind",
        "【向上滑刺】vuwind": "vuwind",
        "【向下滑刺】vdwind": "vdwind"
    }

    # 处理随机转场
    if transition_text == "【【全随机】】":
        # 从所有转场中随机选择（除了随机选项本身）
        all_transitions = [v for k, v in transition_map.items() if not k.startswith("【【")]
        return random.choice(all_transitions)
    elif transition_text == "【【柔 · 随机】】":
        # 从柔和转场中随机选择
        soft_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                          "circleopen", "circleclose", "vertopen", "vertclose",
                          "horzopen", "horzclose", "distance", "diagtl", "diagtr", "diagbl", "diagbr"]
        return random.choice(soft_transitions)
    elif transition_text == "【【硬 · 随机】】":
        # 从硬切转场中随机选择
        hard_transitions = ["wipeleft", "wiperight", "wipeup", "wipedown", "slideleft", "slideright",
                          "slideup", "slidedown", "radial", "pixelize", "zoomin", "wipetl", "wipetr",
                          "wipebl", "wipebr", "hlslice", "hrslice", "vuslice", "vdslice",
                          "hlwind", "hrwind", "vuwind", "vdwind"]
        return random.choice(hard_transitions)

    return transition_map.get(transition_text, "fade")

def merge_videos(videos, output, transition='fade', trans_duration=2.0):
    if len(videos) < 2:
        print("至少需要两个视频")
        return False

    # 如果transition是中文描述，解析为英文转场名称
    if transition.startswith("【"):
        transition = parse_transition_type(transition)
        print(f"解析转场类型: {transition}")

    print(f"使用转场效果: {transition}, 时长: {trans_duration}秒")

    inputs = [f'-i "{v}"' for v in videos]
    filter_complex = []
    video_filters = []
    audio_filters = []

    # 获取公共参数
    common = get_video_info(videos[0])
    target_w, target_h, target_fps = common['width'], common['height'], common['fps']

    # 处理每个视频的视频流
    for i, v in enumerate(videos):
        info = get_video_info(v)
        # 构建视频处理滤镜（缩放 + 帧率转换）
        video_filter = f"[{i}:v]"

        # 添加缩放滤镜（如果需要）
        if info['width'] != target_w or info['height'] != target_h:
            video_filter += f"scale={target_w}:{target_h},"

        # 添加帧率转换滤镜（如果需要）
        if abs(info['fps'] - target_fps) > 0.1:
            video_filter += f"fps={target_fps},"

        # 确保即使没有处理也有一个null滤镜
        if video_filter.endswith(','):
            video_filter = video_filter[:-1]  # 移除最后的逗号
        else:
            video_filter += "null"  # 添加null滤镜保持流程

        video_filter += f"[v{i}]"
        filter_complex.append(video_filter)

    # 构建视频转场滤镜 - 改进版
    for i in range(len(videos) - 1):
        prev_info = get_video_info(videos[i])
        next_info = get_video_info(videos[i+1])

        # 确保转场时长不会超过前后视频的最短时长的50%
        safe_duration = min(trans_duration, prev_info['duration'] * 0.5, next_info['duration'] * 0.5)
        # 设置转场开始位置为前一个视频的末尾
        offset = prev_info['duration'] - safe_duration

        # 为每个转场添加独立的时间戳信息 - 使用单引号包围转场名称
        video_filters.append(f"[v{i}][v{i+1}]xfade=transition='{transition}':duration={safe_duration:.3f}:offset={offset:.3f}[v{i+1}]")

    # 构建音频合并滤镜
    audio_streams = [f"[{i}:a]" for i in range(len(videos))]
    if audio_streams:
        audio_filters.append(f"{' '.join(audio_streams)}amix=inputs={len(audio_streams)}:duration=first:dropout_transition=0[a]")

    # 合并所有滤镜
    filter_complex += video_filters + audio_filters
    filter_str = ';'.join(filter_complex)

    # 构建FFmpeg命令
    cmd = [
        'ffmpeg', *inputs,
        '-filter_complex', f'"{filter_str}"',
        '-map', f'"[v{len(videos)-1}]"',
        '-map', '"[a]"' if audio_filters else '-an',
        '-c:v', 'h264_nvenc' if GPU_ENABLED else 'libx264',
        '-preset', GPU_PRESET if GPU_ENABLED else CPU_PRESET,
        '-b:v', VIDEO_BITRATE,
        '-maxrate', '18000k',
        '-bufsize', '36000k',
        '-c:a', 'aac' if audio_filters else '',
        '-b:a', '320k',
        '-y', f'"{output}"'
    ]

    print("生成的滤镜图:", filter_str)
    print("执行命令:", ' '.join(cmd))

    try:
        result = subprocess.run(
            ' '.join(cmd),
            shell=True,
            capture_output=True,
            text=True,
            encoding='utf-8',
            check=True
        )
        print(f"合并完成：{output}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"FFmpeg错误：{e.stderr}")
        return False

if __name__ == '__main__':
    video_dir = os.path.dirname(os.path.abspath(__file__))
    videos = list_video_files(video_dir)
    output = os.path.join(video_dir, 'merged_output.mp4')

    if not videos:
        print("未找到视频文件")
    else:
        print(f"找到 {len(videos)} 个视频，正在筛选兼容视频...")
        compatible, info = filter_compatible_videos(videos)

        if not compatible:
            print(info)
        else:
            print(f"兼容视频：{len(compatible)} 个，分辨率 {info['width']}x{info['height']}，帧率 {info['fps']}fps")
            # 测试不同的转场时长
            merge_videos(compatible, output, transition='fade', trans_duration=0.5)