#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试授权系统
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_machine_code_verifier():
    """测试机器码验证器（不使用GUI）"""
    print("=" * 50)
    print("测试机器码验证器")
    print("=" * 50)

    try:
        from machine_code_verifier import check_authorization_only

        # 检查授权（不使用GUI）
        valid, message, machine_id, _ = check_authorization_only("TestApp")

        print(f"当前机器码: {machine_id}")

        # 显示授权配置数量
        try:
            from machine_code_verifier import MachineCodeVerifier
            verifier = MachineCodeVerifier("TestApp")
            print(f"已配置授权数量: {len(verifier.authorized_config)}")
        except:
            pass

        print(f"\n授权检查结果:")
        print(f"状态: {'通过' if valid else '失败'}")
        print(f"消息: {message}")

        if valid:
            print("\n✅ 授权验证成功！可以正常使用软件。")
        else:
            print("\n❌ 授权验证失败！无法使用软件。")

        return valid

    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_gui():
    """测试带GUI的授权系统"""
    print("\n" + "=" * 50)
    print("测试GUI授权系统")
    print("=" * 50)
    
    try:
        # 检查PySide6是否可用
        try:
            from PySide6.QtWidgets import QApplication
            print("PySide6 可用，启动GUI测试...")
        except ImportError:
            print("PySide6 不可用，跳过GUI测试")
            return False
        
        from machine_code_verifier import run_application_with_authorization_check
        
        def test_main_app():
            print("🎉 主应用程序启动成功！")
            print("授权验证通过，可以正常使用软件。")
            
            # 创建一个简单的窗口来验证
            try:
                from PySide6.QtWidgets import QMainWindow, QLabel
                from PySide6.QtCore import QTimer
                
                app = QApplication.instance()
                if app is None:
                    app = QApplication(sys.argv)
                
                window = QMainWindow()
                window.setWindowTitle("授权测试成功")
                window.setGeometry(300, 300, 400, 200)
                
                label = QLabel("✅ 授权验证成功！\n软件可以正常使用。")
                label.setStyleSheet("QLabel { padding: 20px; font-size: 14px; }")
                window.setCentralWidget(label)
                window.show()
                
                # 3秒后自动关闭
                timer = QTimer()
                timer.timeout.connect(window.close)
                timer.start(3000)
                
                return app.exec()
            except Exception as e:
                print(f"创建测试窗口失败: {e}")
                return True
        
        print("启动授权检查...")
        result = run_application_with_authorization_check(test_main_app, "TestApp")
        return True
        
    except Exception as e:
        print(f"GUI测试错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("机器码授权系统测试")
    print("=" * 50)
    
    # 测试基本功能
    basic_test_passed = test_machine_code_verifier()
    
    # 如果基本测试通过，进行GUI测试
    if basic_test_passed:
        print("\n基本测试通过，继续GUI测试...")
        gui_test_passed = test_with_gui()
    else:
        print("\n基本测试失败，跳过GUI测试")
        gui_test_passed = False
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"基本功能测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"GUI功能测试: {'✅ 通过' if gui_test_passed else '❌ 失败'}")
    
    if basic_test_passed and gui_test_passed:
        print("\n🎉 所有测试通过！授权系统工作正常。")
    elif basic_test_passed:
        print("\n⚠️ 基本功能正常，但GUI测试失败。可能是PySide6环境问题。")
    else:
        print("\n❌ 测试失败，请检查授权系统配置。")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
