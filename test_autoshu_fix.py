"""
测试自动洗素材功能修复
"""

import sys
import os
from pathlib import Path

# 添加当前目录到路径
sys.path.append('.')

def test_strategy_generation_with_small_files():
    """测试少量文件时的策略生成"""
    print("=== 测试少量文件时的策略生成 ===")
    
    try:
        from video_mixer import VideoMixer
        
        # 创建混剪器实例
        mixer = VideoMixer()
        
        # 设置少量测试文件（模拟用户的情况：2个前贴，2个后贴）
        mixer.main_files = [Path(f"test_main_{i}.mp4") for i in range(2)]
        mixer.variant_files = [Path(f"test_variant_{i}.mp4") for i in range(2)]
        
        print(f"前贴文件数量: {len(mixer.main_files)}")
        print(f"后贴文件数量: {len(mixer.variant_files)}")
        
        # 生成策略
        strategies = mixer.generate_strategies()
        
        print("生成的策略列表:")
        for i, (name, count) in enumerate(strategies, 1):
            print(f"  {i}. {name} → {count} 个视频")
        
        # 检查是否包含"一个前贴只用一次"策略
        strategy_names = [name for name, _ in strategies]
        
        if "一个前贴只用一次" in strategy_names:
            print("✅ '一个前贴只用一次'策略存在")
            
            # 检查数量计算
            expected_count = min(2, 2)  # min(前贴数, 后贴数)
            actual_count = next((count for name, count in strategies if name == "一个前贴只用一次"), None)
            
            if actual_count == expected_count:
                print(f"✅ 策略数量计算正确: {actual_count}")
            else:
                print(f"❌ 策略数量计算错误: 期望{expected_count}, 实际{actual_count}")
        else:
            print("❌ '一个前贴只用一次'策略不存在")
            
        # 检查是否有其他策略
        if len(strategies) > 1:
            print(f"✅ 生成了 {len(strategies)} 个策略")
        else:
            print("⚠️ 只生成了1个策略")
            
        return True
        
    except Exception as e:
        print(f"❌ 策略生成测试失败: {str(e)}")
        return False

def test_video_cleaner_setup():
    """测试洗素材设置"""
    print("\n=== 测试洗素材设置 ===")
    
    try:
        from video_cleaner import VideoCleanerProcessor
        
        processor = VideoCleanerProcessor()
        processor.set_output_directory("test_output")
        processor.set_target_resolution(1080, 1920)
        processor.set_target_bitrate(12000)
        processor.set_target_framerate(30)
        processor.replace_original = False
        
        print("✅ VideoCleanerProcessor 创建成功")
        print(f"✅ 输出目录设置: test_output")
        print(f"✅ 目标分辨率: 1080x1920")
        print(f"✅ 目标比特率: 12000")
        print(f"✅ 目标帧率: 30")
        print(f"✅ 替换原文件模式: {processor.replace_original}")
        
        return True
        
    except Exception as e:
        print(f"❌ 洗素材设置测试失败: {str(e)}")
        return False

def test_autoshu_thread_methods():
    """测试AutoWashMixingThread方法"""
    print("\n=== 测试AutoWashMixingThread方法 ===")
    
    try:
        from main import AutoWashMixingThread
        
        # 检查关键方法是否存在
        required_methods = [
            '_get_temp_dir',
            '_wash_videos', 
            '_get_washed_files',
            '_get_output_files',
            '_wash_output_files',
            '_cleanup_temp_dirs',
            'run'
        ]
        
        for method in required_methods:
            if hasattr(AutoWashMixingThread, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
                
        print("✅ 所有必需方法都存在")
        return True
        
    except Exception as e:
        print(f"❌ AutoWashMixingThread方法测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始测试自动洗素材功能修复...")
    
    results = []
    
    # 测试策略生成
    results.append(test_strategy_generation_with_small_files())
    
    # 测试洗素材设置
    results.append(test_video_cleaner_setup())
    
    # 测试AutoWashMixingThread方法
    results.append(test_autoshu_thread_methods())
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print(f"策略生成测试: {'✅ 通过' if results[0] else '❌ 失败'}")
    print(f"洗素材设置测试: {'✅ 通过' if results[1] else '❌ 失败'}")
    print(f"AutoWashMixingThread方法测试: {'✅ 通过' if results[2] else '❌ 失败'}")
    
    success_count = sum(results)
    total_count = len(results)
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！修复成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
