"""
测试洗素材功能
演示XSC_tab的完整功能流程
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTimer

# 添加当前目录到路径
sys.path.append('.')

from main import MainWindow

def test_xsc_functionality():
    """测试洗素材功能"""
    app = QApplication([])
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    print("=== 洗素材功能测试 ===")
    print()
    
    # 1. 测试初始状态
    print("1. 初始状态检查:")
    print(f"   - 文件列表: {len(window.xsc_files)} 个文件")
    print(f"   - 替换原素材模式: {window.ui.XSC_radioButton_TH_2.isChecked()}")
    print(f"   - 指定输出目录模式: {window.ui.XSC_radioButton_ZD_2.isChecked()}")
    print(f"   - 输出目录输入框启用: {window.ui.XSC_lineEdit_4.isEnabled()}")
    print(f"   - 选择目录按钮启用: {window.ui.XSC_where_4.isEnabled()}")
    print()
    
    # 2. 测试单选按钮切换
    print("2. 测试单选按钮切换:")
    print("   切换到指定输出目录模式...")
    window.ui.XSC_radioButton_ZD_2.setChecked(True)
    print(f"   - 输出目录输入框启用: {window.ui.XSC_lineEdit_4.isEnabled()}")
    print(f"   - 选择目录按钮启用: {window.ui.XSC_where_4.isEnabled()}")
    
    print("   切换回替换原素材模式...")
    window.ui.XSC_radioButton_TH_2.setChecked(True)
    print(f"   - 输出目录输入框启用: {window.ui.XSC_lineEdit_4.isEnabled()}")
    print(f"   - 选择目录按钮启用: {window.ui.XSC_where_4.isEnabled()}")
    print()
    
    # 3. 测试默认参数
    print("3. 默认参数检查:")
    try:
        width = window.ui.SXC_textEdit_W.toPlainText().strip()
        height = window.ui.SXC_textEdit_H.toPlainText().strip()
        bitrate = window.ui.SXC_textEdit_BTL.toPlainText().strip()
        print(f"   - 目标宽度: {width}")
        print(f"   - 目标高度: {height}")
        print(f"   - 目标比特率: {bitrate} kbps")
        print(f"   - 目标帧率: 30fps (固定)")
    except Exception as e:
        print(f"   - 参数读取错误: {e}")
    print()
    
    # 4. 测试文件添加功能（模拟）
    print("4. 测试文件管理功能:")
    
    # 模拟添加文件
    test_files = [
        "C:/test/video1.mp4",
        "C:/test/video2.mp4",
        "C:/test/video3.avi"
    ]
    
    print("   模拟添加文件...")
    window._add_xsc_files(test_files)
    print(f"   - 文件列表: {len(window.xsc_files)} 个文件")
    
    # 显示文件列表
    for i, file_path in enumerate(window.xsc_files):
        print(f"     {i+1}. {Path(file_path).name}")
    print()
    
    # 5. 测试清空功能
    print("5. 测试清空功能:")
    window.clear_xsc_files()
    print(f"   - 清空后文件列表: {len(window.xsc_files)} 个文件")
    print()
    
    # 6. 测试日志功能
    print("6. 测试日志功能:")
    window.update_xsc_log("这是一条测试日志")
    window.update_xsc_log("✅ 成功信息")
    window.update_xsc_log("❌ 错误信息")
    window.update_xsc_log("⚠️ 警告信息")
    print("   - 日志已添加到界面")
    print()
    
    # 7. 测试进度条功能
    print("7. 测试进度条功能:")
    for progress in [0, 25, 50, 75, 100]:
        window.update_xsc_progress(progress)
        print(f"   - 进度: {progress}%")
    print()
    
    # 8. 测试处理按钮状态
    print("8. 测试按钮状态:")
    print(f"   - 开始处理按钮启用: {window.ui.XSC_pushButton_3.isEnabled()}")
    print(f"   - 停止处理按钮启用: {window.ui.XSC_pushButton_4.isEnabled()}")
    print()
    
    print("=== 测试完成 ===")
    print("所有基础功能测试通过！")
    print()
    print("功能说明:")
    print("1. 拖拽视频文件到列表中添加文件")
    print("2. 选择'替换原素材'或'指定输出目录'模式")
    print("3. 设置目标分辨率（默认1080x1920）")
    print("4. 设置目标比特率（默认12000kbps）")
    print("5. 帧率固定为30fps")
    print("6. 点击'开始洗素材'开始处理")
    print("7. 处理过程中可以查看日志和进度")
    print("8. 可以随时点击'停止处理'中断处理")
    
    # 设置定时器关闭应用
    QTimer.singleShot(2000, app.quit)
    
    return app.exec()

if __name__ == "__main__":
    test_xsc_functionality()
