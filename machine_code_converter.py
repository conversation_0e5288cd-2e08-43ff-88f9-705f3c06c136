#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器码转换工具
将机器码转换为MD5值用于授权验证
"""

import hashlib
import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QLabel, QLineEdit, QPushButton, QTextEdit,
                            QGroupBox, QMessageBox, QTabWidget)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

class MachineCodeConverter:
    def __init__(self):
        # 密钥盐值 - 用于增加MD5复杂度，可以自定义
        self.salt_key = "MFChen_Video_Tool_2025_Secret_Key_MDZNB&*"
        
    def convert_machine_code_to_md5(self, machine_code):
        """
        将机器码转换为独特的MD5值
        使用自定义算法增加复杂度
        """
        if not machine_code:
            return ""
        
        # 第一步：机器码 + 盐值
        step1 = machine_code + self.salt_key
        
        # 第二步：反转字符串
        step2 = step1[::-1]
        
        # 第三步：插入额外字符
        step3 = ""
        for i, char in enumerate(step2):
            step3 += char
            if i % 3 == 0:
                step3 += str(i % 10)
        
        # 第四步：再次加盐并生成MD5
        final_string = step3 + self.salt_key + machine_code
        md5_hash = hashlib.md5(final_string.encode('utf-8')).hexdigest()
        
        return md5_hash.upper()  # 返回大写MD5
    
    def batch_convert(self, machine_codes):
        """批量转换机器码"""
        results = {}
        for code in machine_codes:
            if code.strip():
                results[code.strip()] = self.convert_machine_code_to_md5(code.strip())
        return results

class MachineCodeConverterGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.converter = MachineCodeConverter()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("机器码转换工具")
        self.setGeometry(300, 300, 700, 600)

        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)

        # 标题
        title_label = QLabel("机器码转换工具")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # 创建选项卡
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)

        # 单个转换选项卡
        single_tab = QWidget()
        tab_widget.addTab(single_tab, "单个转换")
        self.setup_single_tab(single_tab)

        # 批量转换选项卡
        batch_tab = QWidget()
        tab_widget.addTab(batch_tab, "批量转换")
        self.setup_batch_tab(batch_tab)

    def setup_single_tab(self, tab):
        """设置单个转换选项卡"""
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # 输入区域
        input_group = QGroupBox("输入机器码")
        input_layout = QVBoxLayout()
        input_group.setLayout(input_layout)

        self.single_input = QLineEdit()
        self.single_input.setPlaceholderText("请输入机器码...")
        input_layout.addWidget(self.single_input)

        layout.addWidget(input_group)

        # 转换按钮
        convert_button = QPushButton("转换")
        convert_button.clicked.connect(self.convert_single)
        layout.addWidget(convert_button)

        # 结果区域
        result_group = QGroupBox("转换结果")
        result_layout = QVBoxLayout()
        result_group.setLayout(result_layout)

        self.single_result = QTextEdit()
        self.single_result.setReadOnly(True)
        result_layout.addWidget(self.single_result)

        layout.addWidget(result_group)

    def setup_batch_tab(self, tab):
        """设置批量转换选项卡"""
        layout = QVBoxLayout()
        tab.setLayout(layout)

        # 输入区域
        input_group = QGroupBox("输入机器码列表（每行一个）")
        input_layout = QVBoxLayout()
        input_group.setLayout(input_layout)

        self.batch_input = QTextEdit()
        self.batch_input.setPlaceholderText("请输入机器码列表，每行一个...")
        input_layout.addWidget(self.batch_input)

        layout.addWidget(input_group)

        # 转换按钮
        convert_button = QPushButton("批量转换")
        convert_button.clicked.connect(self.convert_batch)
        layout.addWidget(convert_button)

        # 结果区域
        result_group = QGroupBox("转换结果")
        result_layout = QVBoxLayout()
        result_group.setLayout(result_layout)

        self.batch_result = QTextEdit()
        self.batch_result.setReadOnly(True)
        result_layout.addWidget(self.batch_result)

        layout.addWidget(result_group)

    def convert_single(self):
        """转换单个机器码"""
        machine_code = self.single_input.text().strip()
        if not machine_code:
            QMessageBox.warning(self, "警告", "请输入机器码！")
            return

        auth_code = self.converter.convert_machine_code_to_md5(machine_code)

        result_text = f"机器码: {machine_code}\n授权码: {auth_code}\n\n用于代码中的格式:\n\"{auth_code}\","
        self.single_result.setPlainText(result_text)

    def convert_batch(self):
        """批量转换机器码"""
        input_text = self.batch_input.toPlainText().strip()
        if not input_text:
            QMessageBox.warning(self, "警告", "请输入机器码列表！")
            return
        
        machine_codes = [line.strip() for line in input_text.split('\n') if line.strip()]
        if not machine_codes:
            QMessageBox.warning(self, "警告", "没有找到有效的机器码！")
            return
        
        results = self.converter.batch_convert(machine_codes)
        
        # 格式化结果
        result_text = "转换结果:\n" + "="*50 + "\n\n"

        for machine_code, auth_code in results.items():
            result_text += f"机器码: {machine_code}\n授权码: {auth_code}\n\n"

        result_text += "\n用于代码中的格式:\n" + "="*30 + "\n"
        for machine_code, auth_code in results.items():
            result_text += f"\"{auth_code}\",  # {machine_code}\n"

        self.batch_result.setPlainText(result_text)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("机器码转换工具")
    app.setApplicationVersion("1.0")
    
    window = MachineCodeConverterGUI()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
