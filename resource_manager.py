#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源管理模块
用于处理打包后的资源文件路径
"""

import os
import sys
from pathlib import Path

def get_resource_path(relative_path):
    """
    获取资源文件的绝对路径
    支持开发环境和打包后的环境
    
    Args:
        relative_path (str): 相对于项目根目录的路径
        
    Returns:
        str: 资源文件的绝对路径
    """
    try:
        # PyInstaller打包后的临时目录
        base_path = sys._MEIPASS
    except AttributeError:
        # 开发环境，使用脚本所在目录
        base_path = os.path.dirname(os.path.abspath(__file__))
    
    return os.path.join(base_path, relative_path)

def get_photo_path(filename):
    """
    获取photo文件夹中图片的路径
    
    Args:
        filename (str): 图片文件名
        
    Returns:
        str: 图片文件的绝对路径
    """
    return get_resource_path(os.path.join("photo", filename))

def get_photo_folder_path(subfolder=""):
    """
    获取photo文件夹或其子文件夹的路径
    
    Args:
        subfolder (str): 子文件夹名称，默认为空（返回photo根目录）
        
    Returns:
        str: 文件夹的绝对路径
    """
    if subfolder:
        return get_resource_path(os.path.join("photo", subfolder))
    else:
        return get_resource_path("photo")

def check_photo_resources():
    """
    检查photo文件夹中的必要资源是否存在
    
    Returns:
        dict: 检查结果，包含每个资源的存在状态
    """
    required_resources = {
        "AI生成.png": get_photo_path("AI生成.png")
    }
    
    results = {}
    for name, path in required_resources.items():
        results[name] = {
            "path": path,
            "exists": os.path.exists(path)
        }
    
    return results

def list_photo_resources():
    """
    列出photo文件夹中的所有资源
    
    Returns:
        dict: 包含文件和文件夹列表的字典
    """
    photo_dir = get_photo_folder_path()
    
    if not os.path.exists(photo_dir):
        return {"files": [], "folders": [], "exists": False}
    
    files = []
    folders = []
    
    try:
        for item in os.listdir(photo_dir):
            item_path = os.path.join(photo_dir, item)
            if os.path.isfile(item_path):
                files.append(item)
            elif os.path.isdir(item_path):
                folders.append(item)
    except Exception as e:
        print(f"列出photo资源时出错: {e}")
    
    return {
        "files": files,
        "folders": folders,
        "exists": True,
        "path": photo_dir
    }

def get_others_images():
    """
    获取others文件夹中的所有图片文件
    
    Returns:
        list: 图片文件的完整路径列表
    """
    others_dir = get_photo_folder_path("others")
    
    if not os.path.exists(others_dir):
        return []
    
    image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp']
    image_files = []
    
    try:
        for file in os.listdir(others_dir):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(others_dir, file))
    except Exception as e:
        print(f"获取others图片时出错: {e}")
    
    return image_files

def validate_photo_structure():
    """
    验证photo文件夹的结构是否完整
    
    Returns:
        tuple: (is_valid, missing_items, error_message)
    """
    required_items = [
        ("file", "AI生成.png")
    ]
    
    missing_items = []
    
    # 检查photo文件夹本身是否存在
    photo_dir = get_photo_folder_path()
    if not os.path.exists(photo_dir):
        return False, ["photo文件夹"], "photo文件夹不存在"
    
    # 检查必需的文件和文件夹
    for item_type, item_name in required_items:
        if item_type == "file":
            path = get_photo_path(item_name)
        else:  # folder
            path = get_photo_folder_path(item_name)
        
        if not os.path.exists(path):
            missing_items.append(f"{item_type}: {item_name}")
    
    if missing_items:
        error_message = f"缺少以下资源: {', '.join(missing_items)}"
        return False, missing_items, error_message
    
    # 检查others文件夹是否有图片
    others_images = get_others_images()
    if not others_images:
        missing_items.append("others文件夹中没有图片文件")
        error_message = "others文件夹中没有找到图片文件"
        return False, missing_items, error_message
    
    return True, [], "photo文件夹结构完整"

if __name__ == "__main__":
    """测试资源管理功能"""
    print("=== 资源管理模块测试 ===")
    
    # 测试基本路径获取
    print(f"项目根目录: {get_resource_path('')}")
    print(f"photo文件夹: {get_photo_folder_path()}")
    print(f"AI生成.png路径: {get_photo_path('AI生成.png')}")
    
    # 检查资源
    print("\n=== 资源检查 ===")
    resources = check_photo_resources()
    for name, info in resources.items():
        status = "✅ 存在" if info["exists"] else "❌ 缺失"
        print(f"{name}: {status} - {info['path']}")
    
    # 列出资源
    print("\n=== 资源列表 ===")
    photo_resources = list_photo_resources()
    if photo_resources["exists"]:
        print(f"photo文件夹路径: {photo_resources['path']}")
        print(f"文件: {photo_resources['files']}")
        print(f"文件夹: {photo_resources['folders']}")
    else:
        print("photo文件夹不存在")
    
    # 验证结构
    print("\n=== 结构验证 ===")
    is_valid, missing, message = validate_photo_structure()
    print(f"结构完整性: {'✅ 完整' if is_valid else '❌ 不完整'}")
    print(f"验证结果: {message}")
    if missing:
        print(f"缺失项目: {missing}")
    
    # 获取others图片
    print("\n=== others图片 ===")
    others_images = get_others_images()
    if others_images:
        print(f"找到 {len(others_images)} 张图片:")
        for img in others_images[:5]:  # 只显示前5张
            print(f"  - {os.path.basename(img)}")
        if len(others_images) > 5:
            print(f"  ... 还有 {len(others_images) - 5} 张图片")
    else:
        print("others文件夹中没有找到图片")
