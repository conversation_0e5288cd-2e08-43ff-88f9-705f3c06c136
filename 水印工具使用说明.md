# 水印工具使用说明

## 功能概述

新增的【tab_SY】水印工具可以为1080*1920分辨率的视频批量添加水印，支持四大类水印方式。

## 界面布局

### 素材设定区【SY_groupBox_3】
- **视频文件列表**: 显示要添加水印的视频文件
- **添加文件**: 选择要处理的视频文件
- **移除选中**: 移除选中的视频文件
- **清空列表**: 清空所有视频文件
- **输出设置**: 
  - 替换原素材：直接覆盖原视频文件
  - 指定输出目录：将处理后的视频保存到指定文件夹

### 水印指定区【SY_groupBox_sy】

#### A组水印类型（单选）：
1. **无** - 【SY_radioButton_nothing】
   - 不添加任何水印
   - 直接复制原文件
   - 可配合AI标识使用

2. **动态合合LOGO (15%)** - 【SY_radioButton_HHLOGO_2】
   - 添加动态移动的logo水印
   - 透明度：15%
   - Logo图片：HHlogo.png

3. **全屏合合LOGO (6%)** - 【SY_radioButton_HHLOGO】
   - 添加全屏图片水印
   - 透明度：6%
   - 图片：HHlogo整张水印.png

4. **预设全屏图片随机 (10%)** - 【SY_radioButton_HHTPSJ】
   - 从预设文件夹随机选择图片作为全屏水印
   - 透明度：10%
   - 文件夹：photo\others

5. **合合LOGO角标 (100%)** - 【SY_radioButton_HHlogoJB】
   - 添加固定位置的logo水印
   - 透明度：100%（不透明）
   - Logo图片：HHlogo.png
   - 位置由B组选项决定

6. **自定义 (仅全屏图片)** - 【SY_radioButton_ZD】
   - 从用户指定文件夹随机选择图片
   - 透明度：用户自定义
   - 文件夹：用户选择

#### B组位置选择（仅在选择角标模式时可用）：
- **左上** - 【radioButton_LUP】
- **右上** - 【radioButton_RUP】
- **左下** - 【radioButton_LDW】
- **右下** - 【radioButton_RDW】

#### 自定义设置（仅在选择自定义模式时可用）：
- **文件夹选择** - 【SY_lineEdit_6ZD】和【SY_where_6ZD】
- **不透明度** - 【SY_spinBox_ZDSZ】（0-100%）

### 核心处理区【SY_groupBox_3KS_2】
- **处理日志** - 【SY_plainTextEdit_3RZ_2】：显示处理过程信息
- **顽刻炼化** - 【SY_pushButton_5】：开始处理按钮
- **哎，你先等等** - 【SY_pushButton_6】：停止处理按钮
- **进度条** - 【SY_progressBar_4】：显示处理进度

## 使用步骤

1. **添加视频文件**
   - 点击"添加文件"选择要处理的视频
   - 支持拖拽添加
   - 仅支持1080*1920分辨率的视频

2. **选择水印类型**
   - 从A组中选择一种水印类型
   - 如选择角标模式，需在B组中选择位置
   - 如选择自定义模式，需设置文件夹和透明度

3. **设置输出方式**
   - 替换原素材：直接覆盖原文件
   - 指定输出目录：保存到新位置

4. **开始处理**
   - 点击"顽刻炼化"开始处理
   - 可随时点击"哎，你先等等"停止处理

## 注意事项

1. **分辨率要求**：仅支持1080*1920分辨率的视频
2. **文件格式**：支持mp4、avi、mov、mkv、wmv、flv、webm格式
3. **FFmpeg依赖**：需要正确安装FFmpeg
4. **设置保存**：自定义文件夹路径和透明度设置会自动保存
5. **界面反色**：支持界面反色功能，按钮颜色会相应调整

## 技术参数

- **视频编码器**：libx264
- **编码预设**：medium
- **视频比特率**：12000k
- **最大比特率**：15000k
- **缓冲区大小**：24000k
- **音频处理**：保持原音频不变

## 故障排除

1. **FFmpeg未找到**：检查FFmpeg是否正确安装在预设路径
2. **分辨率错误**：确保视频为1080*1920分辨率
3. **图片文件缺失**：检查photo文件夹中的图片文件是否存在
4. **权限问题**：确保对输出目录有写入权限
