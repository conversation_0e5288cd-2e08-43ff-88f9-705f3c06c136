#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试便携授权码功能
"""

import sys
from datetime import datetime, timedelta

def test_portable_auth_generator():
    """测试便携授权码生成器"""
    print("=" * 50)
    print("测试便携授权码生成器")
    print("=" * 50)
    
    try:
        from portable_auth_generator import PortableAuthGenerator
        
        generator = PortableAuthGenerator()
        
        # 生成一个3天有效期的授权码
        start_date = datetime.now().date()
        end_date = start_date + timedelta(days=3)
        user_id = "测试用户A"
        
        print(f"生成授权码:")
        print(f"开始日期: {start_date}")
        print(f"结束日期: {end_date}")
        print(f"用户标识: {user_id}")
        
        auth_code = generator.generate_portable_auth_code(start_date, end_date, user_id)
        print(f"生成的授权码: {auth_code}")
        
        # 验证授权码
        valid, auth_data, message = generator.verify_portable_auth_code(auth_code)
        print(f"\n验证结果: {message}")
        if auth_data:
            print(f"授权信息: {auth_data}")
        
        return auth_code if valid else None
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_portable_auth_verifier(auth_code):
    """测试便携授权码验证器"""
    print("\n" + "=" * 50)
    print("测试便携授权码验证器")
    print("=" * 50)
    
    try:
        from portable_auth_verifier import PortableAuthVerifier
        
        verifier = PortableAuthVerifier("测试应用")
        
        # 验证授权码
        valid, auth_data, message = verifier.verify_portable_auth_code(auth_code)
        print(f"验证结果: {message}")
        if auth_data:
            print(f"授权信息: {auth_data}")
        
        # 检查是否有有效授权
        has_valid, valid_message = verifier.has_valid_portable_auth()
        print(f"\n有效授权检查: {valid_message}")
        
        # 显示当前有效授权
        active_auths = verifier.get_active_portable_auths()
        if active_auths:
            print("\n当前有效的便携授权码:")
            for auth in active_auths:
                print(f"- {auth['start_date']} 至 {auth['end_date']} (剩余{auth['days_left']}天)")
                if auth['user']:
                    print(f"  用户: {auth['user']}")
        
        return valid
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_machine_code_integration():
    """测试与机器码授权系统的集成"""
    print("\n" + "=" * 50)
    print("测试与机器码授权系统的集成")
    print("=" * 50)
    
    try:
        from machine_code_verifier import check_authorization_only
        
        # 检查授权（包含便携授权码）
        valid, message, machine_id, md5_value = check_authorization_only("测试应用")
        
        print(f"当前机器码: {machine_id}")
        print(f"授权检查结果: {'通过' if valid else '失败'}")
        print(f"消息: {message}")
        
        return valid
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("便携授权码功能测试")
    print("=" * 60)
    
    # 1. 测试生成器
    auth_code = test_portable_auth_generator()
    if not auth_code:
        print("❌ 便携授权码生成失败")
        return 1
    
    # 2. 测试验证器
    if not test_portable_auth_verifier(auth_code):
        print("❌ 便携授权码验证失败")
        return 1
    
    # 3. 测试集成
    if test_machine_code_integration():
        print("\n✅ 所有测试通过！便携授权码功能正常工作。")
        return 0
    else:
        print("\n⚠️ 机器码授权未通过，但便携授权码功能正常。")
        return 0

if __name__ == "__main__":
    sys.exit(main())
