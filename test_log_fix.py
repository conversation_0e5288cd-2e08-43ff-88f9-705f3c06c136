#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志功能修复
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow
from PySide6.QtCore import QTimer
from PySide6.QtGui import QTextCursor

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import MainWindow

def test_log_functions():
    """测试日志功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 等待界面初始化
    def after_init():
        print("=== 开始测试日志功能 ===")
        
        # 测试水印日志
        print("测试水印日志功能...")
        try:
            window.update_sy_log("测试水印日志消息 1")
            window.update_sy_log("测试水印日志消息 2")
            window.update_sy_log("✅ 水印日志测试成功")
            print("✅ 水印日志功能正常")
        except Exception as e:
            print(f"❌ 水印日志功能失败: {str(e)}")
        
        # 测试洗素材日志
        print("测试洗素材日志功能...")
        try:
            window.update_xsc_log("测试洗素材日志消息 1")
            window.update_xsc_log("测试洗素材日志消息 2")
            window.update_xsc_log("✅ 洗素材日志测试成功")
            print("✅ 洗素材日志功能正常")
        except Exception as e:
            print(f"❌ 洗素材日志功能失败: {str(e)}")
        
        # 测试QTextCursor API
        print("测试QTextCursor API...")
        try:
            from PySide6.QtGui import QTextCursor
            # 测试正确的API调用
            cursor = window.ui.SY_plainTextEdit_3RZ_2.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            print("✅ QTextCursor.MoveOperation.End API正常")
        except Exception as e:
            print(f"❌ QTextCursor API失败: {str(e)}")
        
        # 等待一段时间后退出
        QTimer.singleShot(2000, app.quit)
    
    # 延迟执行测试
    QTimer.singleShot(1000, after_init)
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_log_functions()
