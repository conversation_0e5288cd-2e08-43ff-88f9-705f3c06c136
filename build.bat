@echo off
chcp 65001 >nul
echo ========================================
echo    MFChen视频混剪工具 打包脚本
echo ========================================
echo.

echo 1. 检查打包环境...
python build_config.py
if errorlevel 1 (
    echo.
    echo ❌ 环境检查失败，请解决问题后重试
    pause
    exit /b 1
)

echo.
echo 2. 开始打包...
echo.

REM 使用PyInstaller进行打包
pyinstaller --onefile --windowed --clean --noconfirm ^
    --name "MFChen视频混剪工具" ^
    --add-data "photo;photo" ^
    --hidden-import "resource_manager" ^
    --hidden-import "utils" ^
    --hidden-import "video_mixer" ^
    --hidden-import "machine_code_verifier" ^
    --hidden-import "portable_auth_verifier" ^
    --hidden-import "startup_optimizer" ^
    --exclude-module "tkinter" ^
    --exclude-module "matplotlib" ^
    --exclude-module "numpy" ^
    --exclude-module "scipy" ^
    --exclude-module "pandas" ^
    main.py

if errorlevel 1 (
    echo.
    echo ❌ 打包失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ 打包完成！
echo.
echo 输出文件位置: dist\MFChen视频混剪工具.exe
echo.
echo 注意事项:
echo - photo文件夹已自动包含在exe中
echo - 可以直接分发exe文件，无需额外文件
echo - 首次运行可能需要一些时间来解压资源
echo ========================================
echo.
pause
