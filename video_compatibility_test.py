#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频兼容性测试工具
用于测试和诊断视频文件的编码兼容性问题
"""

import os
import sys
import subprocess
from pathlib import Path

def get_video_detailed_info(video_path):
    """获取视频文件的详细编码信息"""
    try:
        # 查找FFprobe
        ffprobe_paths = [
            "D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffprobe.exe",
            "C:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffprobe.exe",
            "E:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffprobe.exe",
            "F:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffprobe.exe"
        ]
        
        ffprobe_path = None
        for path in ffprobe_paths:
            if os.path.exists(path):
                ffprobe_path = path
                break
                
        if not ffprobe_path:
            print("❌ 未找到FFprobe")
            return None
            
        cmd = [
            ffprobe_path,
            "-v", "error",
            "-show_streams",
            "-show_format",
            "-of", "json",
            str(video_path)
        ]
        
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            timeout=30
        )
        
        if result.returncode == 0:
            import json
            return json.loads(result.stdout)
        else:
            print(f"❌ FFprobe错误: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 获取视频信息失败: {str(e)}")
        return None

def analyze_video_compatibility(video_path):
    """分析视频兼容性"""
    print(f"\n🔍 分析视频: {os.path.basename(video_path)}")
    print(f"📁 路径: {video_path}")
    
    info = get_video_detailed_info(video_path)
    if not info:
        return False
        
    # 分析视频流
    video_streams = [s for s in info.get('streams', []) if s.get('codec_type') == 'video']
    audio_streams = [s for s in info.get('streams', []) if s.get('codec_type') == 'audio']
    
    print(f"\n📺 视频流信息:")
    for i, stream in enumerate(video_streams):
        codec = stream.get('codec_name', 'unknown')
        width = stream.get('width', 'unknown')
        height = stream.get('height', 'unknown')
        pix_fmt = stream.get('pix_fmt', 'unknown')
        fps = stream.get('r_frame_rate', 'unknown')
        
        print(f"  流 {i}: {codec} {width}x{height} {pix_fmt} {fps}fps")
        
        # 检查兼容性
        compatibility_issues = []
        if codec != 'h264':
            compatibility_issues.append(f"编码器不是H.264 (当前: {codec})")
        if pix_fmt != 'yuv420p':
            compatibility_issues.append(f"像素格式不是yuv420p (当前: {pix_fmt})")
            
        if compatibility_issues:
            print(f"  ⚠️ 兼容性问题: {', '.join(compatibility_issues)}")
        else:
            print(f"  ✅ 视频流兼容")
    
    print(f"\n🔊 音频流信息:")
    for i, stream in enumerate(audio_streams):
        codec = stream.get('codec_name', 'unknown')
        sample_rate = stream.get('sample_rate', 'unknown')
        channels = stream.get('channels', 'unknown')
        
        print(f"  流 {i}: {codec} {sample_rate}Hz {channels}ch")
        
        # 检查兼容性
        compatibility_issues = []
        if codec != 'aac':
            compatibility_issues.append(f"编码器不是AAC (当前: {codec})")
        if sample_rate != '48000':
            compatibility_issues.append(f"采样率不是48kHz (当前: {sample_rate}Hz)")
        if channels != 2:
            compatibility_issues.append(f"不是双声道 (当前: {channels}ch)")
            
        if compatibility_issues:
            print(f"  ⚠️ 兼容性问题: {', '.join(compatibility_issues)}")
        else:
            print(f"  ✅ 音频流兼容")
    
    # 分析容器格式
    format_info = info.get('format', {})
    format_name = format_info.get('format_name', 'unknown')
    print(f"\n📦 容器格式: {format_name}")
    
    # 检查时间戳问题
    start_time = format_info.get('start_time', '0')
    if float(start_time) != 0:
        print(f"  ⚠️ 起始时间不为0: {start_time}")
    else:
        print(f"  ✅ 时间戳正常")
    
    return True

def test_video_files(folder_path):
    """测试文件夹中的所有视频文件"""
    if not os.path.exists(folder_path):
        print(f"❌ 文件夹不存在: {folder_path}")
        return
        
    video_extensions = ['.mp4', '.avi', '.mkv', '.mov']
    video_files = []
    
    for ext in video_extensions:
        video_files.extend(Path(folder_path).glob(f"*{ext}"))
        video_files.extend(Path(folder_path).glob(f"*{ext.upper()}"))
    
    if not video_files:
        print(f"❌ 在文件夹中未找到视频文件: {folder_path}")
        return
        
    print(f"🎬 找到 {len(video_files)} 个视频文件")
    
    compatible_count = 0
    incompatible_files = []
    
    for video_file in video_files:
        try:
            if analyze_video_compatibility(video_file):
                compatible_count += 1
            else:
                incompatible_files.append(video_file)
        except Exception as e:
            print(f"❌ 分析失败: {video_file} - {str(e)}")
            incompatible_files.append(video_file)
    
    print(f"\n📊 兼容性统计:")
    print(f"  ✅ 兼容文件: {compatible_count}")
    print(f"  ⚠️ 问题文件: {len(incompatible_files)}")
    
    if incompatible_files:
        print(f"\n🔧 建议对以下文件进行预处理:")
        for file in incompatible_files:
            print(f"  - {os.path.basename(file)}")

if __name__ == "__main__":
    print("🎬 视频兼容性测试工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        test_path = sys.argv[1]
    else:
        test_path = input("请输入要测试的文件夹路径: ").strip()
    
    if os.path.isfile(test_path):
        analyze_video_compatibility(test_path)
    elif os.path.isdir(test_path):
        test_video_files(test_path)
    else:
        print(f"❌ 路径不存在: {test_path}")
