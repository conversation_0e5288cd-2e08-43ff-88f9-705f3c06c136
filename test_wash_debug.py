"""
测试洗素材功能调试
"""

import sys
import os
from pathlib import Path

# 添加当前目录到路径
sys.path.append('.')

def test_video_cleaner_direct():
    """直接测试VideoCleanerProcessor"""
    print("=== 直接测试VideoCleanerProcessor ===")
    
    try:
        from video_cleaner import VideoCleanerProcessor
        
        # 创建测试输出目录
        test_output_dir = Path("test_wash_output")
        test_output_dir.mkdir(exist_ok=True)
        
        processor = VideoCleanerProcessor()
        processor.set_output_directory(str(test_output_dir))
        processor.set_target_resolution(1080, 1920)
        processor.set_target_bitrate(12000)
        processor.set_target_framerate(30)
        processor.replace_original = False
        
        print("✅ VideoCleanerProcessor 创建成功")
        print(f"✅ 输出目录: {test_output_dir}")
        print(f"✅ replace_original: {processor.replace_original}")
        
        # 检查关键方法
        if hasattr(processor, '_process_single_file'):
            print("✅ _process_single_file 方法存在")
        else:
            print("❌ _process_single_file 方法不存在")
            return False
            
        if hasattr(processor, '_build_ffmpeg_command'):
            print("✅ _build_ffmpeg_command 方法存在")
        else:
            print("❌ _build_ffmpeg_command 方法不存在")
            return False
            
        # 测试构建FFmpeg命令
        test_input = "test_input.mp4"
        test_output = str(test_output_dir / "test_output.mp4")
        
        try:
            cmd = processor._build_ffmpeg_command(test_input, test_output)
            print(f"✅ FFmpeg命令构建成功: {cmd[:100]}...")
        except Exception as e:
            print(f"❌ FFmpeg命令构建失败: {str(e)}")
            return False
            
        # 清理测试目录
        try:
            test_output_dir.rmdir()
        except:
            pass
            
        return True
        
    except Exception as e:
        print(f"❌ VideoCleanerProcessor测试失败: {str(e)}")
        return False

def test_mixer_running_state():
    """测试混剪器运行状态"""
    print("\n=== 测试混剪器运行状态 ===")
    
    try:
        from video_mixer import VideoMixer
        
        mixer = VideoMixer()
        
        print(f"初始运行状态: {mixer._is_running}")
        
        # 设置运行状态
        mixer._is_running = True
        print(f"设置后运行状态: {mixer._is_running}")
        
        # 重置运行状态
        mixer._is_running = False
        print(f"重置后运行状态: {mixer._is_running}")
        
        print("✅ 混剪器运行状态测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 混剪器运行状态测试失败: {str(e)}")
        return False

def test_temp_dir_creation():
    """测试临时目录创建"""
    print("\n=== 测试临时目录创建 ===")
    
    try:
        from video_mixer import VideoMixer
        from pathlib import Path
        
        mixer = VideoMixer()
        
        # 模拟获取临时目录
        ffmpeg_dir = Path(mixer.ffmpeg_path).parent
        temp_dir = ffmpeg_dir / "测试临时目录"
        
        print(f"FFmpeg目录: {ffmpeg_dir}")
        print(f"临时目录路径: {temp_dir}")
        
        # 创建临时目录
        temp_dir.mkdir(exist_ok=True)
        
        if temp_dir.exists():
            print("✅ 临时目录创建成功")
            
            # 清理测试目录
            temp_dir.rmdir()
            print("✅ 临时目录清理成功")
            return True
        else:
            print("❌ 临时目录创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 临时目录创建测试失败: {str(e)}")
        return False

def test_autoshu_thread_init():
    """测试AutoWashMixingThread初始化"""
    print("\n=== 测试AutoWashMixingThread初始化 ===")
    
    try:
        from main import AutoWashMixingThread
        from video_mixer import VideoMixer
        
        mixer = VideoMixer()
        
        # 模拟main_window（简化版）
        class MockMainWindow:
            pass
        
        main_window = MockMainWindow()
        
        # 创建AutoWashMixingThread
        thread = AutoWashMixingThread(mixer, main_window)
        
        print("✅ AutoWashMixingThread 创建成功")
        print(f"✅ mixer引用: {thread.mixer is not None}")
        print(f"✅ main_window引用: {thread.main_window is not None}")
        
        # 检查信号
        if hasattr(thread, 'log_signal'):
            print("✅ log_signal 存在")
        else:
            print("❌ log_signal 不存在")
            
        if hasattr(thread, 'progress_signal'):
            print("✅ progress_signal 存在")
        else:
            print("❌ progress_signal 不存在")
            
        if hasattr(thread, 'finished'):
            print("✅ finished 信号存在")
        else:
            print("❌ finished 信号不存在")
            
        return True
        
    except Exception as e:
        print(f"❌ AutoWashMixingThread初始化测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("开始洗素材功能调试测试...")
    
    results = []
    
    # 测试VideoCleanerProcessor
    results.append(test_video_cleaner_direct())
    
    # 测试混剪器运行状态
    results.append(test_mixer_running_state())
    
    # 测试临时目录创建
    results.append(test_temp_dir_creation())
    
    # 测试AutoWashMixingThread初始化
    results.append(test_autoshu_thread_init())
    
    # 汇总结果
    print("\n" + "="*50)
    print("调试测试结果汇总:")
    print(f"VideoCleanerProcessor测试: {'✅ 通过' if results[0] else '❌ 失败'}")
    print(f"混剪器运行状态测试: {'✅ 通过' if results[1] else '❌ 失败'}")
    print(f"临时目录创建测试: {'✅ 通过' if results[2] else '❌ 失败'}")
    print(f"AutoWashMixingThread初始化测试: {'✅ 通过' if results[3] else '❌ 失败'}")
    
    success_count = sum(results)
    total_count = len(results)
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有调试测试通过！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
