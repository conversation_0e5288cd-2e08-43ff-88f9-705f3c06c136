#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试水印功能的简单脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_watermark_import():
    """测试水印相关的导入是否正常"""
    try:
        # 测试主要模块导入
        from main import MainWindow, SYWatermarkThread
        print("✅ 主要模块导入成功")
        
        # 测试UI模块导入
        from ui_main_window import Ui_mainWindow
        print("✅ UI模块导入成功")
        
        # 测试工具模块导入
        from utils import get_video_metadata, sanitize_filename
        print("✅ 工具模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_watermark_paths():
    """测试水印相关路径是否存在"""
    paths_to_check = [
        r"E:\000混剪文件夹\可视化project-pyside6\photo\HHlogo.png",
        r"E:\000混剪文件夹\可视化project-pyside6\photo\HHlogo整张水印.png",
        r"E:\000混剪文件夹\可视化project-pyside6\photo\others"
    ]
    
    all_exist = True
    for path in paths_to_check:
        if os.path.exists(path):
            print(f"✅ 路径存在: {path}")
        else:
            print(f"❌ 路径不存在: {path}")
            all_exist = False
    
    return all_exist

def test_ffmpeg_detection():
    """测试FFmpeg检测功能"""
    try:
        from pathlib import Path
        
        ffmpeg_paths = [
            Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
            Path("C:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
            Path("E:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
            Path("F:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
        ]

        ffmpeg_found = False
        for path in ffmpeg_paths:
            if path.exists():
                print(f"✅ 找到FFmpeg: {path}")
                ffmpeg_found = True
                break
        
        if not ffmpeg_found:
            print("❌ 未找到FFmpeg")
        
        return ffmpeg_found
        
    except Exception as e:
        print(f"❌ FFmpeg检测失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 水印功能测试 ===")
    print()
    
    # 测试导入
    print("1. 测试模块导入...")
    import_ok = test_watermark_import()
    print()
    
    # 测试路径
    print("2. 测试水印资源路径...")
    paths_ok = test_watermark_paths()
    print()
    
    # 测试FFmpeg
    print("3. 测试FFmpeg检测...")
    ffmpeg_ok = test_ffmpeg_detection()
    print()
    
    # 总结
    print("=== 测试结果 ===")
    if import_ok and paths_ok and ffmpeg_ok:
        print("✅ 所有测试通过，水印功能应该可以正常使用")
        return True
    else:
        print("❌ 部分测试失败，请检查相关配置")
        if not import_ok:
            print("  - 模块导入失败")
        if not paths_ok:
            print("  - 水印资源路径缺失")
        if not ffmpeg_ok:
            print("  - FFmpeg未找到")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
