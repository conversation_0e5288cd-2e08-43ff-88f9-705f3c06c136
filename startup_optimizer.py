#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动优化模块
用于优化程序启动速度
"""

import os
import sys

class StartupOptimizer:
    def __init__(self):
        self.is_frozen = getattr(sys, 'frozen', False)
        
    def optimize_imports(self):
        """优化导入，预加载常用模块"""
        if self.is_frozen:
            # 在打包环境中，预加载核心模块
            try:
                # 预加载PySide6核心模块
                import PySide6.QtCore
                import PySide6.QtWidgets
                import PySide6.QtGui
            except ImportError:
                pass
    
    def set_environment_variables(self):
        """设置环境变量优化启动"""
        if self.is_frozen:
            # 禁用一些不必要的功能
            os.environ['QT_LOGGING_RULES'] = '*.debug=false'
            os.environ['QT_QUICK_CONTROLS_STYLE'] = 'Basic'
            
            # 优化Qt性能
            os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '0'
            os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '0'
    
    def optimize_startup(self):
        """执行所有启动优化"""
        self.set_environment_variables()
        self.optimize_imports()

# 全局优化器实例
optimizer = StartupOptimizer()
optimizer.optimize_startup()
