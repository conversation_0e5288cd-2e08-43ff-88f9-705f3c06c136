import sys
from PySide6.QtWidgets import QApplication, QMainWindow
from ui_main_window import Ui_mainWindow

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        # 创建 UI 对象
        self.ui = Ui_mainWindow()
        # 设置 UI
        self.ui.setupUi(self)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())