#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的TransitionMixer
"""

import tempfile
import os
from pathlib import Path
from transition_mixer import TransitionMixer

def create_test_video(output_path, duration=3):
    """创建测试视频"""
    import subprocess
    
    ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    CREATE_NO_WINDOW = 0x08000000
    
    cmd = [
        str(ffmpeg_path),
        "-f", "lavfi",
        "-i", f"color=red:duration={duration}:size=320x240:rate=30",
        "-f", "lavfi", 
        "-i", f"sine=frequency=440:duration={duration}",
        "-c:v", "libx264",
        "-c:a", "aac",
        "-t", str(duration),
        "-y",
        str(output_path)
    ]
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        creationflags=CREATE_NO_WINDOW
    )
    
    return result.returncode == 0 and os.path.exists(output_path)

def test_transition_mixer():
    """测试TransitionMixer的回退机制"""
    print("🎬 测试TransitionMixer回退机制")
    print("=" * 50)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试视频
        video1 = temp_path / "test1.mp4"
        video2 = temp_path / "test2.mp4"
        
        print("🎬 创建测试视频...")
        if not create_test_video(video1):
            print("❌ 无法创建测试视频1")
            return
        
        if not create_test_video(video2):
            print("❌ 无法创建测试视频2")
            return
        
        print("✅ 测试视频创建成功")
        
        # 创建TransitionMixer实例
        ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
        ffprobe_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffprobe.exe")
        
        def log_callback(message):
            print(f"  {message}")
        
        mixer = TransitionMixer(ffmpeg_path, ffprobe_path, log_callback)
        
        # 测试1: 启用响度统一的转场
        print("\n🔍 测试1: 启用响度统一的转场")
        mixer.set_loudnorm_enabled(True)
        output1 = temp_path / "output_loudnorm.mp4"
        
        success1 = mixer.merge_two_videos_with_transition(
            video1, video2, output1,
            "fade", 1.0, False
        )
        
        print(f"结果: {'✅ 成功' if success1 else '❌ 失败'}")
        if success1:
            print(f"文件大小: {os.path.getsize(output1)} bytes")
        
        # 测试2: 禁用响度统一的转场
        print("\n🔍 测试2: 禁用响度统一的转场")
        mixer.set_loudnorm_enabled(False)
        output2 = temp_path / "output_no_loudnorm.mp4"
        
        success2 = mixer.merge_two_videos_with_transition(
            video1, video2, output2,
            "fade", 1.0, False
        )
        
        print(f"结果: {'✅ 成功' if success2 else '❌ 失败'}")
        if success2:
            print(f"文件大小: {os.path.getsize(output2)} bytes")
        
        # 测试3: 静音模式
        print("\n🔍 测试3: 静音模式")
        output3 = temp_path / "output_muted.mp4"
        
        success3 = mixer.merge_two_videos_with_transition(
            video1, video2, output3,
            "fade", 1.0, True  # 静音
        )
        
        print(f"结果: {'✅ 成功' if success3 else '❌ 失败'}")
        if success3:
            print(f"文件大小: {os.path.getsize(output3)} bytes")
        
        # 汇总结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        print(f"  响度统一转场: {'✅ 成功' if success1 else '❌ 失败'}")
        print(f"  简单转场: {'✅ 成功' if success2 else '❌ 失败'}")
        print(f"  静音转场: {'✅ 成功' if success3 else '❌ 失败'}")
        
        if success1 and success2 and success3:
            print("\n✅ 所有测试通过！TransitionMixer工作正常")
        elif success2 or success3:
            print("\n⚠️ 部分测试通过，回退机制有效")
        else:
            print("\n❌ 所有测试失败，需要进一步检查")

if __name__ == "__main__":
    test_transition_mixer()
