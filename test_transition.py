#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转场功能测试脚本
用于验证转场效果是否正常工作
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from 转场合并 import merge_videos, parse_transition_type

def create_test_video(output_path, duration=5, color="red", text="Test"):
    """创建测试视频"""
    cmd = [
        "ffmpeg",
        "-f", "lavfi",
        "-i", f"color={color}:size=1920x1080:duration={duration}",
        "-f", "lavfi", 
        "-i", f"sine=frequency=1000:duration={duration}",
        "-vf", f"drawtext=text='{text}':fontsize=60:fontcolor=white:x=(w-text_w)/2:y=(h-text_h)/2",
        "-c:v", "libx264",
        "-c:a", "aac",
        "-y",
        output_path
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0
    except Exception as e:
        print(f"创建测试视频失败: {e}")
        return False

def test_transitions():
    """测试转场功能"""
    print("🎬 开始转场功能测试...")
    
    # 创建测试目录
    test_dir = Path("test_transitions")
    test_dir.mkdir(exist_ok=True)
    
    # 创建测试视频
    video1 = test_dir / "test1.mp4"
    video2 = test_dir / "test2.mp4"
    
    print("📹 创建测试视频...")
    if not create_test_video(str(video1), duration=3, color="red", text="Video 1"):
        print("❌ 创建第一个测试视频失败")
        return False
        
    if not create_test_video(str(video2), duration=3, color="blue", text="Video 2"):
        print("❌ 创建第二个测试视频失败")
        return False
    
    print("✅ 测试视频创建完成")
    
    # 测试不同的转场效果
    transitions_to_test = [
        ("fade", "淡入淡出"),
        ("wipeleft", "向左擦除"),
        ("smoothleft", "柔和向左擦除"),
        ("circleopen", "圆形展开"),
        ("【柔】【叠化】fade", "中文转场描述"),
        ("【【全随机】】", "全随机转场"),
        ("【【柔 · 随机】】", "柔和随机转场")
    ]
    
    success_count = 0
    total_count = len(transitions_to_test)
    
    for transition, description in transitions_to_test:
        print(f"\n🔄 测试转场: {description} ({transition})")
        
        output_file = test_dir / f"output_{transition.replace('【', '').replace('】', '').replace(' ', '_').replace('·', '')}.mp4"
        
        try:
            success = merge_videos(
                [str(video1), str(video2)],
                str(output_file),
                transition=transition,
                trans_duration=1.0
            )
            
            if success and output_file.exists():
                file_size = output_file.stat().st_size
                print(f"✅ 转场测试成功: {description} (文件大小: {file_size} bytes)")
                success_count += 1
            else:
                print(f"❌ 转场测试失败: {description}")
                
        except Exception as e:
            print(f"❌ 转场测试异常: {description} - {str(e)}")
    
    print(f"\n📊 测试结果: {success_count}/{total_count} 个转场效果测试成功")
    
    # 清理测试文件（可选）
    cleanup = input("\n🗑️  是否清理测试文件? (y/N): ").lower().strip()
    if cleanup == 'y':
        import shutil
        shutil.rmtree(test_dir)
        print("✅ 测试文件已清理")
    else:
        print(f"📁 测试文件保留在: {test_dir.absolute()}")
    
    return success_count == total_count

def test_transition_parsing():
    """测试转场解析功能"""
    print("\n🔍 测试转场解析功能...")
    
    test_cases = [
        ("【柔】【叠化】fade", "fade"),
        ("【向左擦除】wipeleft", "wipeleft"),
        ("【【全随机】】", None),  # 随机结果
        ("【【柔 · 随机】】", None),  # 随机结果
        ("【【硬 · 随机】】", None),  # 随机结果
        ("【柔】【圆形展开】circleopen", "circleopen"),
        ("【时钟擦除】radial", "radial"),
        ("【像素模糊】pixelize", "pixelize"),
        ("【放大转场】zoomin", "zoomin"),
    ]
    
    success_count = 0
    for input_text, expected in test_cases:
        try:
            result = parse_transition_type(input_text)
            if expected is None:
                # 对于随机转场，只检查结果不为空
                if result and result != input_text:
                    print(f"✅ {input_text} -> {result} (随机)")
                    success_count += 1
                else:
                    print(f"❌ {input_text} -> {result} (随机失败)")
            else:
                if result == expected:
                    print(f"✅ {input_text} -> {result}")
                    success_count += 1
                else:
                    print(f"❌ {input_text} -> {result} (期望: {expected})")
        except Exception as e:
            print(f"❌ {input_text} -> 异常: {e}")
    
    print(f"📊 解析测试结果: {success_count}/{len(test_cases)} 个测试通过")
    return success_count == len(test_cases)

if __name__ == "__main__":
    print("🚀 转场功能完整测试")
    print("=" * 50)
    
    # 检查FFmpeg是否可用
    try:
        subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)
        print("✅ FFmpeg 可用")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ FFmpeg 不可用，请确保已安装FFmpeg并添加到PATH")
        sys.exit(1)
    
    # 测试转场解析
    parsing_ok = test_transition_parsing()
    
    # 测试转场合并
    merging_ok = test_transitions()
    
    print("\n" + "=" * 50)
    if parsing_ok and merging_ok:
        print("🎉 所有测试通过！转场功能正常工作")
        sys.exit(0)
    else:
        print("⚠️  部分测试失败，请检查转场功能")
        sys.exit(1)
