#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单修复验证测试脚本
用于验证音频呲呲啦啦声音修复和转场随机选择范围修复
"""

def test_audio_fix():
    """测试音频处理修复"""
    print("🔍 测试一：音频处理修复验证")
    
    # 检查main.py中的音频处理代码
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查是否包含修复的音频处理逻辑
        fixes_found = []
        
        if "amix=inputs=2:duration=first:normalize=0[aout]" in content:
            fixes_found.append("✅ 背景音乐混合修复已应用")
        
        if "amix=inputs=2:duration=longest:normalize=0[outa]" in content:
            fixes_found.append("✅ 转场音频混合修复已应用")
            
        if 'volume=0.8[a0]' in content and 'amix=inputs=' in content and ':normalize=0[aout]' in content:
            fixes_found.append("✅ 多视频音频混合修复已应用")
            
        if '"-b:a", "256k"' in content and '"-ar", "48000"' in content:
            fixes_found.append("✅ 音频编码参数优化已应用")
            
        if fixes_found:
            for fix in fixes_found:
                print(fix)
            return True
        else:
            print("❌ 音频处理修复未找到")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_transition_fix():
    """测试转场随机选择范围修复"""
    print("\n🔍 测试二：转场随机选择范围修复验证")
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查转场效果列表
        fixes_found = []
        
        # 检查关键转场特效是否存在
        key_effects = ["hlslice", "hrslice", "vuslice", "vdslice", "hlwind", "hrwind", "vuwind", "vdwind"]
        missing_effects = [effect for effect in key_effects if effect not in content]
        
        if not missing_effects:
            fixes_found.append("✅ 转场特效列表已更新（包含所有42个特效）")
        else:
            fixes_found.append(f"❌ 转场特效列表不完整，缺少：{missing_effects}")
            
        # 检查柔和转场数量
        soft_effects = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                       "circleopen", "circleclose", "vertopen", "vertclose",
                       "horzopen", "horzclose", "distance", "diagtl", "diagtr", "diagbl", "diagbr"]
        soft_found = sum(1 for effect in soft_effects if effect in content)
        
        if soft_found >= 16:
            fixes_found.append(f"✅ 柔和转场列表完整（{soft_found}个特效）")
        else:
            fixes_found.append(f"❌ 柔和转场列表不完整（仅{soft_found}个特效）")
            
        for fix in fixes_found:
            print(fix)
            
        return not missing_effects and soft_found >= 16
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_transition_mixer_fix():
    """测试transition_mixer.py中的修复"""
    print("\n🔍 测试三：transition_mixer.py修复验证")
    
    try:
        with open('transition_mixer.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        fixes_found = []
        
        # 检查音频混合修复
        if "amix=inputs=2:duration=longest:normalize=0[outa]" in content:
            fixes_found.append("✅ transition_mixer音频混合修复已应用")
            
        # 检查音频编码参数
        if '"-b:a", "256k"' in content and '"-ar", "48000"' in content:
            fixes_found.append("✅ transition_mixer音频编码参数优化已应用")
            
        # 检查转场列表
        expected_effects = ["hlslice", "hrslice", "vuslice", "vdslice", "hlwind", "hrwind", "vuwind", "vdwind"]
        if all(effect in content for effect in expected_effects):
            fixes_found.append("✅ transition_mixer转场列表已更新")
            
        for fix in fixes_found:
            print(fix)
            
        return len(fixes_found) >= 2
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_video_mixer_fix():
    """测试video_mixer.py中的修复"""
    print("\n🔍 测试四：video_mixer.py修复验证")
    
    try:
        with open('video_mixer.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        fixes_found = []
        
        # 检查音频混合修复
        if "amix=inputs=2:duration=longest:normalize=0[outa]" in content:
            fixes_found.append("✅ video_mixer音频混合修复已应用")
            
        # 检查音频编码参数
        if '"-b:a", "256k"' in content and '"-ar", "48000"' in content:
            fixes_found.append("✅ video_mixer音频编码参数优化已应用")
            
        for fix in fixes_found:
            print(fix)
            
        return len(fixes_found) >= 1
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎬 修复验证测试开始")
    print("=" * 50)
    
    results = []
    
    # 执行各项测试
    results.append(test_audio_fix())
    results.append(test_transition_fix())
    results.append(test_transition_mixer_fix())
    results.append(test_video_mixer_fix())
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 所有测试通过！({passed}/{total})")
        print("\n✅ 修复内容：")
        print("1. 音频呲呲啦啦声音修复：添加normalize=0参数，优化音频编码")
        print("2. 转场随机选择范围修复：更新为完整的42个特效列表")
        print("3. 音频编码参数优化：提高码率到256k，采样率到48kHz")
        print("4. 多文件音频混合优化：添加音量控制，避免失真")
    else:
        print(f"⚠️ 部分测试失败 ({passed}/{total})")
        print("请检查修复是否完整")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n退出代码: {0 if success else 1}")
