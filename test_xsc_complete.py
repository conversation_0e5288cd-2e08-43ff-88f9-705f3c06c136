"""
洗素材功能完整测试
验证所有功能包括设置保存
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QSettings

# 添加当前目录到路径
sys.path.append('.')

from main import MainWindow

def test_complete_xsc_functionality():
    """完整测试洗素材功能"""
    app = QApplication([])
    
    print("=== 洗素材功能完整测试 ===")
    print()
    
    # 1. 创建主窗口
    window = MainWindow()
    window.show()
    
    print("1. 初始化检查:")
    print(f"   - 程序启动成功")
    print(f"   - 洗素材页签已加载")
    print(f"   - 默认模式: {'替换原素材' if window.ui.XSC_radioButton_TH_2.isChecked() else '指定输出目录'}")
    print()
    
    # 2. 测试设置保存和加载
    print("2. 设置功能测试:")
    
    # 修改设置
    custom_width = "1440"
    custom_height = "2560"
    custom_bitrate = "18000"
    
    window.ui.SXC_textEdit_W.setPlainText(custom_width)
    window.ui.SXC_textEdit_H.setPlainText(custom_height)
    window.ui.SXC_textEdit_BTL.setPlainText(custom_bitrate)
    
    print(f"   - 设置自定义分辨率: {custom_width}x{custom_height}")
    print(f"   - 设置自定义比特率: {custom_bitrate}kbps")
    
    # 等待自动保存
    app.processEvents()
    
    # 验证保存
    settings = QSettings("YourCompany", "YourAppName")
    saved_width = settings.value("SXC_textEdit_W", "")
    saved_height = settings.value("SXC_textEdit_H", "")
    saved_bitrate = settings.value("SXC_textEdit_BTL", "")
    
    settings_ok = (saved_width == custom_width and 
                   saved_height == custom_height and 
                   saved_bitrate == custom_bitrate)
    
    print(f"   - 设置自动保存: {'✅ 成功' if settings_ok else '❌ 失败'}")
    print()
    
    # 3. 测试模式切换
    print("3. 模式切换测试:")
    
    # 切换到指定输出目录模式
    window.ui.XSC_radioButton_ZD_2.setChecked(True)
    lineEdit_enabled = window.ui.XSC_lineEdit_4.isEnabled()
    where_enabled = window.ui.XSC_where_4.isEnabled()
    
    print(f"   - 切换到指定输出目录模式")
    print(f"   - 输出目录输入框启用: {'✅ 是' if lineEdit_enabled else '❌ 否'}")
    print(f"   - 选择目录按钮启用: {'✅ 是' if where_enabled else '❌ 否'}")
    
    # 切换回替换原素材模式
    window.ui.XSC_radioButton_TH_2.setChecked(True)
    lineEdit_disabled = not window.ui.XSC_lineEdit_4.isEnabled()
    where_disabled = not window.ui.XSC_where_4.isEnabled()
    
    print(f"   - 切换回替换原素材模式")
    print(f"   - 输出目录输入框禁用: {'✅ 是' if lineEdit_disabled else '❌ 否'}")
    print(f"   - 选择目录按钮禁用: {'✅ 是' if where_disabled else '❌ 否'}")
    print()
    
    # 4. 测试文件管理
    print("4. 文件管理测试:")
    
    # 模拟添加文件
    test_files = [
        "C:/test/video1.mp4",
        "C:/test/video2.avi",
        "C:/test/video3.mkv"
    ]
    
    window._add_xsc_files(test_files)
    files_added = len(window.xsc_files) == 3
    
    print(f"   - 添加3个测试文件: {'✅ 成功' if files_added else '❌ 失败'}")
    print(f"   - 文件列表数量: {len(window.xsc_files)}")
    
    # 测试清空
    window.clear_xsc_files()
    files_cleared = len(window.xsc_files) == 0
    
    print(f"   - 清空文件列表: {'✅ 成功' if files_cleared else '❌ 失败'}")
    print()
    
    # 5. 测试日志和进度
    print("5. 日志和进度测试:")
    
    # 测试日志
    test_messages = [
        "开始洗素材处理...",
        "✅ 处理成功: video1.mp4",
        "❌ 处理失败: video2.avi",
        "⚠️ 警告: 参数可能不正确"
    ]
    
    for msg in test_messages:
        window.update_xsc_log(msg)
    
    print(f"   - 日志功能: ✅ 正常")
    
    # 测试进度条
    for progress in [0, 25, 50, 75, 100]:
        window.update_xsc_progress(progress)
    
    print(f"   - 进度条功能: ✅ 正常")
    print()
    
    # 6. 测试按钮状态
    print("6. 按钮状态测试:")
    
    start_enabled = window.ui.XSC_pushButton_3.isEnabled()
    stop_enabled = window.ui.XSC_pushButton_4.isEnabled()
    
    print(f"   - 开始处理按钮: {'✅ 启用' if start_enabled else '❌ 禁用'}")
    print(f"   - 停止处理按钮: {'✅ 启用' if stop_enabled else '❌ 禁用'}")
    print()
    
    # 7. 测试参数验证
    print("7. 参数验证测试:")
    
    # 获取当前参数
    try:
        width = int(window.ui.SXC_textEdit_W.toPlainText().strip())
        height = int(window.ui.SXC_textEdit_H.toPlainText().strip())
        bitrate = int(window.ui.SXC_textEdit_BTL.toPlainText().strip())
        
        params_valid = width > 0 and height > 0 and bitrate > 0
        
        print(f"   - 当前参数: {width}x{height}, {bitrate}kbps")
        print(f"   - 参数有效性: {'✅ 有效' if params_valid else '❌ 无效'}")
        
    except ValueError:
        print(f"   - 参数格式: ❌ 无效")
        params_valid = False
    
    print()
    
    # 8. 重新启动测试设置持久化
    print("8. 设置持久化测试:")
    
    window.close()
    window2 = MainWindow()
    
    # 检查设置是否正确加载
    loaded_width = window2.ui.SXC_textEdit_W.toPlainText()
    loaded_height = window2.ui.SXC_textEdit_H.toPlainText()
    loaded_bitrate = window2.ui.SXC_textEdit_BTL.toPlainText()
    
    persistence_ok = (loaded_width == custom_width and 
                     loaded_height == custom_height and 
                     loaded_bitrate == custom_bitrate)
    
    print(f"   - 重启后设置加载: {'✅ 正确' if persistence_ok else '❌ 错误'}")
    print(f"   - 加载的参数: {loaded_width}x{loaded_height}, {loaded_bitrate}kbps")
    print()
    
    # 9. 总结测试结果
    print("=== 测试结果总结 ===")
    
    all_tests = [
        ("设置保存", settings_ok),
        ("模式切换", lineEdit_enabled and where_enabled and lineEdit_disabled and where_disabled),
        ("文件管理", files_added and files_cleared),
        ("参数验证", params_valid),
        ("设置持久化", persistence_ok)
    ]
    
    passed_tests = sum(1 for _, result in all_tests if result)
    total_tests = len(all_tests)
    
    print(f"测试通过: {passed_tests}/{total_tests}")
    print()
    
    for test_name, result in all_tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   - {test_name}: {status}")
    
    print()
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！洗素材功能完全正常！")
        print()
        print("✅ 功能特性:")
        print("   - 支持拖拽添加视频文件")
        print("   - 支持替换原素材和指定输出目录两种模式")
        print("   - 自动保存和加载用户设置（分辨率、比特率）")
        print("   - 实时日志显示和进度跟踪")
        print("   - 参数验证和错误处理")
        print("   - 完整的UI交互功能")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    window2.close()
    
    # 设置定时器关闭应用
    QTimer.singleShot(2000, app.quit)
    
    return app.exec()

if __name__ == "__main__":
    test_complete_xsc_functionality()
