#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
便携授权码验证器
用于验证便携授权码的有效性，包含时间回退检测
"""

import hashlib
import hmac
import base64
import json
import os
import time
from datetime import datetime, timedelta

class PortableAuthVerifier:
    def __init__(self, app_name="DefaultApp"):
        # 加密密钥和盐值（与生成端保持一致）
        self.secret_key = "MFChen_Portable_Auth_2024_Secret_Key"
        self.salt = "MFChen_Salt_2024"
        self.app_name = app_name
        
        # 便携授权码时间记录文件
        self.portable_time_file = os.path.join(
            os.path.expanduser("~"), 
            f".mfchen_portable_time_{app_name.replace(' ', '_')}.dat"
        )
        
        # 便携授权码存储文件
        self.portable_auth_file = os.path.join(
            os.path.expanduser("~"), 
            f".mfchen_portable_auth_{app_name.replace(' ', '_')}.dat"
        )
    
    def _generate_signature(self, data_str):
        """生成数据签名"""
        return hmac.new(
            self.secret_key.encode(),
            data_str.encode(),
            hashlib.sha256
        ).hexdigest()[:16]  # 取前16位作为签名
    
    def _encrypt_data(self, data):
        """简单加密数据"""
        data_str = json.dumps(data)
        return base64.b64encode(data_str.encode()).decode()
    
    def _decrypt_data(self, encrypted_data):
        """简单解密数据"""
        try:
            data_str = base64.b64decode(encrypted_data).decode()
            return json.loads(data_str)
        except:
            return None
    
    def _load_portable_time_record(self):
        """加载便携授权码时间记录"""
        if not os.path.exists(self.portable_time_file):
            return {"first_use": None, "last_use": None}
        
        try:
            with open(self.portable_time_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read().strip()
                data = self._decrypt_data(encrypted_data)
                if data:
                    return data
        except:
            pass
        
        return {"first_use": None, "last_use": None}
    
    def _save_portable_time_record(self, time_data):
        """保存便携授权码时间记录"""
        try:
            encrypted_data = self._encrypt_data(time_data)
            with open(self.portable_time_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
        except:
            pass
    
    def _load_portable_auth_records(self):
        """加载便携授权码记录"""
        if not os.path.exists(self.portable_auth_file):
            return []
        
        try:
            with open(self.portable_auth_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read().strip()
                data = self._decrypt_data(encrypted_data)
                if data and isinstance(data, list):
                    return data
        except:
            pass
        
        return []
    
    def _save_portable_auth_records(self, auth_records):
        """保存便携授权码记录"""
        try:
            encrypted_data = self._encrypt_data(auth_records)
            with open(self.portable_auth_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
        except:
            pass
    
    def _check_time_rollback(self):
        """检查时间回退"""
        current_time = int(time.time())
        time_record = self._load_portable_time_record()
        
        # 首次使用
        if time_record["first_use"] is None:
            time_record["first_use"] = current_time
            time_record["last_use"] = current_time
            self._save_portable_time_record(time_record)
            return False, "首次使用"
        
        # 检查时间回退（12小时 = 43200秒）
        if current_time < (time_record["last_use"] - 43200):
            return True, "检测到时间回退，便携授权码失效"
        
        # 更新最后使用时间
        time_record["last_use"] = current_time
        self._save_portable_time_record(time_record)
        
        return False, "时间验证通过"
    
    def verify_portable_auth_code(self, auth_code):
        """
        验证便携授权码
        
        返回: (是否有效, 授权信息字典, 错误信息)
        """
        try:
            # 1. 检查时间回退
            time_rollback, time_message = self._check_time_rollback()
            if time_rollback:
                return False, None, "授权验证失败，请联系管理员"

            # 2. 检查前缀
            if not auth_code.startswith("PAC_"):
                return False, None, "无效的授权码格式"
            
            # 3. 解码
            encoded_data = auth_code[4:]  # 去掉前缀
            json_str = base64.b64decode(encoded_data).decode()
            final_data = json.loads(json_str)
            
            # 4. 提取数据和签名
            auth_data = final_data.get("data", {})
            signature = final_data.get("sig", "")
            
            # 5. 验证签名
            data_str = json.dumps(auth_data, sort_keys=True, separators=(',', ':'))
            expected_signature = self._generate_signature(data_str)
            
            if signature != expected_signature:
                return False, None, "授权码验证失败"
            
            # 6. 检查日期格式
            start_date = datetime.strptime(auth_data["start"], "%Y-%m-%d")
            end_date = datetime.strptime(auth_data["end"], "%Y-%m-%d")

            # 7. 检查授权码有效期是否超过7天（防破解检查）
            auth_duration = (end_date.date() - start_date.date()).days
            if auth_duration > 7:
                return False, None, "授权码验证失败，请联系管理员"

            # 8. 检查当前时间是否在有效期内
            current_date = datetime.now().date()
            if current_date < start_date.date():
                return False, auth_data, "授权码尚未生效"
            elif current_date > end_date.date():
                return False, auth_data, "授权码已过期"
            
            # 9. 检查是否已经使用过此授权码（防止重复使用）
            auth_records = self._load_portable_auth_records()
            auth_code_hash = hashlib.md5(auth_code.encode()).hexdigest()
            
            # 查找是否已存在
            existing_record = None
            for record in auth_records:
                if record.get("code_hash") == auth_code_hash:
                    existing_record = record
                    break
            
            if existing_record:
                # 已存在，检查是否在有效期内
                if current_date <= end_date.date():
                    return True, auth_data, "授权码验证通过"
                else:
                    return False, auth_data, "授权码已过期"
            else:
                # 新的授权码，记录激活
                new_record = {
                    "code_hash": auth_code_hash,
                    "start_date": auth_data["start"],
                    "end_date": auth_data["end"],
                    "user": auth_data.get("user", ""),
                    "activated_time": int(time.time())
                }
                auth_records.append(new_record)
                
                # 清理过期记录（保留最近10个）
                auth_records = sorted(auth_records, key=lambda x: x.get("activated_time", 0), reverse=True)[:10]
                self._save_portable_auth_records(auth_records)
                
                return True, auth_data, "授权码验证通过"

        except Exception as e:
            return False, None, f"授权码解析错误: {str(e)}"
    
    def add_portable_auth_code(self, auth_code):
        """
        添加便携授权码（用于支持多个授权码的续期功能）
        
        返回: (是否成功, 消息)
        """
        valid, auth_data, message = self.verify_portable_auth_code(auth_code)
        
        if valid:
            return True, f"授权码添加成功: {message}"
        else:
            return False, f"授权码添加失败: {message}"
    
    def get_active_portable_auths(self):
        """
        获取当前有效的便携授权码信息
        
        返回: 有效授权码列表
        """
        auth_records = self._load_portable_auth_records()
        current_date = datetime.now().date()
        
        active_auths = []
        for record in auth_records:
            try:
                end_date = datetime.strptime(record["end_date"], "%Y-%m-%d").date()
                if current_date <= end_date:
                    active_auths.append({
                        "start_date": record["start_date"],
                        "end_date": record["end_date"],
                        "user": record.get("user", ""),
                        "days_left": (end_date - current_date).days
                    })
            except:
                continue
        
        return active_auths
    
    def has_valid_portable_auth(self):
        """
        检查是否有有效的便携授权码
        
        返回: (是否有效, 消息)
        """
        # 检查时间回退
        time_rollback, time_message = self._check_time_rollback()
        if time_rollback:
            return False, "授权验证失败，请联系管理员"

        active_auths = self.get_active_portable_auths()

        if active_auths:
            # 找到最长有效期的授权码
            longest_auth = max(active_auths, key=lambda x: x["days_left"])
            return True, f"授权码有效"
        else:
            return False, "没有有效的授权码"

# 使用示例
if __name__ == "__main__":
    verifier = PortableAuthVerifier("TestApp")
    
    # 测试验证
    test_code = input("请输入授权码: ").strip()
    if test_code:
        valid, auth_data, message = verifier.verify_portable_auth_code(test_code)
        print(f"验证结果: {message}")
        if auth_data:
            print(f"授权信息: {auth_data}")

    # 显示当前有效授权码
    active_auths = verifier.get_active_portable_auths()
    if active_auths:
        print("\n当前有效的授权码:")
        for auth in active_auths:
            print(f"- 用户: {auth['user'] if auth['user'] else '未指定'}")
    else:
        print("\n没有有效的授权码")
