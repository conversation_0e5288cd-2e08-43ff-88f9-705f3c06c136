#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试随机图片选择逻辑
"""

import sys
import os
import random
import hashlib
import glob

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_random_image_selection():
    """测试随机图片选择逻辑"""
    print("=== 测试随机图片选择逻辑 ===")
    
    # 模拟_overlay_random_image_on_video的逻辑
    def select_random_image(input_video, folder_path):
        """模拟随机图片选择"""
        # 查找图片文件
        image_patterns = ["*.png", "*.jpg", "*.jpeg", "*.bmp", "*.tiff", "*.webp"]
        image_files = []
        for pattern in image_patterns:
            image_files.extend(glob.glob(os.path.join(folder_path, pattern)))
            image_files.extend(glob.glob(os.path.join(folder_path, pattern.upper())))
        
        if not image_files:
            return None
        
        # 基于视频文件名生成一致的随机选择
        video_name = os.path.basename(input_video)
        seed = int(hashlib.md5(video_name.encode()).hexdigest()[:8], 16)
        random.seed(seed)
        
        # 对图片文件列表排序，确保一致性
        image_files.sort()
        selected_image = random.choice(image_files)
        
        return selected_image, video_name, seed % 10000
    
    # 测试用例
    test_videos = [
        "video1.mp4",
        "video2.mp4", 
        "video3.avi",
        "test_video.mov",
        "sample.mkv"
    ]
    
    folder_path = "photo/others"
    
    if not os.path.exists(folder_path):
        print(f"❌ 测试文件夹不存在: {folder_path}")
        return False
    
    print(f"测试文件夹: {folder_path}")
    
    # 获取可用图片
    image_patterns = ["*.png", "*.jpg", "*.jpeg", "*.bmp", "*.tiff", "*.webp"]
    available_images = []
    for pattern in image_patterns:
        available_images.extend(glob.glob(os.path.join(folder_path, pattern)))
        available_images.extend(glob.glob(os.path.join(folder_path, pattern.upper())))
    
    print(f"可用图片数量: {len(available_images)}")
    
    if len(available_images) == 0:
        print("❌ 没有找到图片文件")
        return False
    
    print("\n=== 测试不同视频的图片选择 ===")
    selections = {}
    
    for video in test_videos:
        result = select_random_image(video, folder_path)
        if result:
            selected_image, video_name, seed = result
            image_name = os.path.basename(selected_image)
            selections[video] = image_name
            print(f"📹 {video_name} (种子:{seed}) → 🖼️ {image_name}")
        else:
            print(f"❌ {video} → 没有找到图片")
    
    print("\n=== 测试一致性（同一视频多次选择） ===")
    test_video = "consistency_test.mp4"
    
    results = []
    for i in range(5):
        result = select_random_image(test_video, folder_path)
        if result:
            selected_image, video_name, seed = result
            image_name = os.path.basename(selected_image)
            results.append(image_name)
    
    if len(set(results)) == 1:
        print(f"✅ 一致性测试通过: {test_video} 始终选择 {results[0]}")
    else:
        print(f"❌ 一致性测试失败: {test_video} 选择了不同图片: {set(results)}")
        return False
    
    print("\n=== 测试随机性（不同视频选择不同图片） ===")
    unique_selections = len(set(selections.values()))
    total_videos = len(selections)
    
    print(f"不同图片数量: {unique_selections}/{total_videos}")
    
    if unique_selections > 1:
        print("✅ 随机性测试通过: 不同视频选择了不同图片")
    else:
        print("⚠️ 随机性较低: 多个视频选择了相同图片（这在图片数量少时是正常的）")
    
    return True

def test_old_vs_new_logic():
    """对比旧逻辑和新逻辑"""
    print("\n=== 对比旧逻辑和新逻辑 ===")
    
    folder_path = "photo/others"
    test_video = "example.mp4"
    
    if not os.path.exists(folder_path):
        print(f"❌ 测试文件夹不存在: {folder_path}")
        return False
    
    # 获取图片列表
    image_patterns = ["*.png", "*.jpg", "*.jpeg", "*.bmp", "*.tiff", "*.webp"]
    image_files = []
    for pattern in image_patterns:
        image_files.extend(glob.glob(os.path.join(folder_path, pattern)))
    
    if not image_files:
        print("❌ 没有图片文件")
        return False
    
    # 旧逻辑：完全随机
    def old_logic():
        return random.choice(image_files)
    
    # 新逻辑：基于视频名的一致随机
    def new_logic(video_name):
        seed = int(hashlib.md5(video_name.encode()).hexdigest()[:8], 16)
        random.seed(seed)
        image_files_sorted = sorted(image_files)
        return random.choice(image_files_sorted)
    
    print("旧逻辑（完全随机）- 同一视频多次选择:")
    old_results = []
    for i in range(5):
        selected = old_logic()
        image_name = os.path.basename(selected)
        old_results.append(image_name)
        print(f"  第{i+1}次: {image_name}")
    
    print(f"旧逻辑一致性: {len(set(old_results))} 种不同结果")
    
    print("\n新逻辑（基于视频名的一致随机）- 同一视频多次选择:")
    new_results = []
    for i in range(5):
        selected = new_logic(test_video)
        image_name = os.path.basename(selected)
        new_results.append(image_name)
        print(f"  第{i+1}次: {image_name}")
    
    print(f"新逻辑一致性: {len(set(new_results))} 种不同结果")
    
    if len(set(old_results)) > 1 and len(set(new_results)) == 1:
        print("✅ 逻辑改进成功:")
        print("  - 旧逻辑: 每次都不同（不一致）")
        print("  - 新逻辑: 每次都相同（一致）")
        return True
    else:
        print("❌ 逻辑改进可能有问题")
        return False

def main():
    """主测试函数"""
    print("=== 随机图片选择逻辑测试 ===")
    print("测试SY_radioButton_HHTPSJ对应的随机图片选择功能")
    print()
    
    # 测试随机选择
    selection_ok = test_random_image_selection()
    
    # 对比逻辑
    logic_ok = test_old_vs_new_logic()
    
    print("\n=== 测试结果 ===")
    if selection_ok and logic_ok:
        print("✅ 所有测试通过")
        print("🎉 随机图片选择逻辑正确：")
        print("   1. ✅ 每个视频都会选择一张随机图片")
        print("   2. ✅ 同一视频每次处理都选择相同图片（一致性）")
        print("   3. ✅ 不同视频会选择不同图片（随机性）")
        print("   4. ✅ 基于视频文件名生成种子，确保可重现")
        print("\n💡 SY_radioButton_HHTPSJ功能说明:")
        print("   - 从photo/others文件夹随机选择图片作为水印")
        print("   - 每个视频对应固定的图片选择")
        print("   - 不同视频会选择不同的图片")
        print("   - 选择结果可重现和一致")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
