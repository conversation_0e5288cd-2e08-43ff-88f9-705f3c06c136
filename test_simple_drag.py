#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的拖拽功能测试
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sy_methods():
    """测试sy相关方法"""
    try:
        # 导入必要的模块
        from main import MainWindow
        
        # 创建一个模拟的MainWindow实例来测试方法
        class MockMainWindow:
            def __init__(self):
                self.sy_files = []
                
                # 模拟sy_model
                class MockModel:
                    def __init__(self):
                        self._list = []
                    def setStringList(self, items):
                        self._list = items
                    def stringList(self):
                        return self._list
                
                self.sy_model = MockModel()
            
            def update_sy_list(self):
                """模拟update_sy_list方法"""
                file_names = [os.path.basename(file) for file in self.sy_files]
                self.sy_model.setStringList(file_names)
                print(f"更新列表: {file_names}")
            
            def _add_sy_files(self, files):
                """测试_add_sy_files方法"""
                new_files = []
                for file in files:
                    if file not in self.sy_files:
                        new_files.append(file)
                
                if new_files:
                    self.sy_files.extend(new_files)
                    self.update_sy_list()
                    print(f"添加了 {len(new_files)} 个新文件")
                else:
                    print("没有新文件被添加")
        
        # 测试
        print("=== 测试sy方法 ===")
        mock_window = MockMainWindow()
        
        # 测试添加文件
        test_files = ["video1.mp4", "video2.avi", "video3.mov"]
        print(f"测试添加文件: {test_files}")
        mock_window._add_sy_files(test_files)
        
        # 测试重复添加
        print(f"测试重复添加: {test_files[:2]}")
        mock_window._add_sy_files(test_files[:2])
        
        # 检查最终结果
        print(f"最终sy_files: {mock_window.sy_files}")
        print(f"最终模型列表: {mock_window.sy_model.stringList()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_drag_logic():
    """测试拖拽逻辑"""
    try:
        print("=== 测试拖拽逻辑 ===")
        
        # 模拟add_files_from_drag方法的逻辑
        def mock_add_files_from_drag(files, list_type):
            print(f"拖放的文件: {files}")
            print(f"列表类型: {list_type}")
            
            # 模拟视频文件过滤
            video_extensions = [".mp4", ".avi", ".mkv", ".mov", ".flv", ".wmv"]
            video_files = []
            for f in files:
                if any(f.lower().endswith(ext) for ext in video_extensions):
                    video_files.append(f)
            
            print(f"筛选后的视频文件: {video_files}")
            
            if not video_files:
                print("❌ 没有视频文件")
                return False
            
            if list_type == "sy":
                print("✅ 处理sy类型拖拽")
                return True
            else:
                print(f"处理其他类型拖拽: {list_type}")
                return True
        
        # 测试不同的文件类型
        test_cases = [
            (["video.mp4", "video.avi"], "sy"),
            (["image.jpg", "document.txt"], "sy"),
            (["video.MP4", "VIDEO.AVI"], "sy"),
            (["mixed.mp4", "image.jpg", "video.mov"], "sy"),
        ]
        
        for files, list_type in test_cases:
            print(f"\n测试用例: {files} -> {list_type}")
            result = mock_add_files_from_drag(files, list_type)
            print(f"结果: {'成功' if result else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 简单拖拽功能测试 ===")
    print()
    
    # 测试sy方法
    methods_ok = test_sy_methods()
    print()
    
    # 测试拖拽逻辑
    logic_ok = test_drag_logic()
    print()
    
    # 总结
    print("=== 测试结果 ===")
    if methods_ok and logic_ok:
        print("✅ 所有测试通过")
        print("💡 拖拽功能的逻辑是正确的")
        print("💡 如果界面中仍然无法拖拽，可能的原因：")
        print("   1. UI控件的拖拽事件没有正确绑定")
        print("   2. 拖拽的文件路径格式问题")
        print("   3. 权限或其他系统级问题")
        return True
    else:
        print("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
