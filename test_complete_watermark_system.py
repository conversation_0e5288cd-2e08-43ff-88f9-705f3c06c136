#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整水印系统测试
测试所有水印类型与AI标识的组合
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_complete_watermark_system():
    """测试完整水印系统"""
    print("=== 完整水印系统测试 ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from main import MainWindow
        
        app = QApplication(sys.argv)
        window = MainWindow()
        
        print("✅ 主窗口创建成功")
        
        # 1. 测试所有水印类型单选框
        print("\n1. 测试所有水印类型单选框:")
        
        watermark_buttons = [
            ('SY_radioButton_nothing', '无'),
            ('SY_radioButton_HHLOGO_2', '动态合合LOGO'),
            ('SY_radioButton_HHLOGO', '全屏合合LOGO'),
            ('SY_radioButton_HHTPSJ', '预设全屏图片随机'),
            ('SY_radioButton_HHlogoJB', '合合LOGO角标'),
            ('SY_radioButton_ZD', '自定义')
        ]
        
        all_buttons_exist = True
        for button_name, description in watermark_buttons:
            if hasattr(window.ui, button_name):
                button = getattr(window.ui, button_name)
                print(f"   ✅ {button_name} ({description}): 存在，文本='{button.text()}'")
            else:
                print(f"   ❌ {button_name} ({description}): 不存在")
                all_buttons_exist = False
        
        if not all_buttons_exist:
            return False
        
        # 2. 测试AI标识复选框
        print("\n2. 测试AI标识复选框:")
        
        if hasattr(window.ui, 'checkBox_AI'):
            print(f"   ✅ checkBox_AI存在，文本='{window.ui.checkBox_AI.text()}'")
        else:
            print("   ❌ checkBox_AI不存在")
            return False
        
        # 3. 测试单选按钮组功能
        print("\n3. 测试单选按钮组功能:")
        
        if hasattr(window, 'sy_watermark_group'):
            buttons = window.sy_watermark_group.buttons()
            button_count = len(buttons)
            print(f"   水印类型组包含 {button_count} 个按钮")
            
            if button_count == 6:  # 包括新增的"无"选项
                print("   ✅ 水印类型组按钮数量正确")
            else:
                print(f"   ❌ 水印类型组按钮数量错误，期望6个，实际{button_count}个")
                return False
        else:
            print("   ❌ sy_watermark_group不存在")
            return False
        
        # 4. 测试各种组合场景
        print("\n4. 测试各种组合场景:")
        
        test_scenarios = [
            ("无水印 + 无AI标识", 'SY_radioButton_nothing', False),
            ("无水印 + AI标识", 'SY_radioButton_nothing', True),
            ("动态LOGO + 无AI标识", 'SY_radioButton_HHLOGO_2', False),
            ("动态LOGO + AI标识", 'SY_radioButton_HHLOGO_2', True),
            ("全屏LOGO + AI标识", 'SY_radioButton_HHLOGO', True),
            ("角标LOGO + AI标识", 'SY_radioButton_HHlogoJB', True),
        ]
        
        for scenario_name, watermark_button, ai_enabled in test_scenarios:
            print(f"\n   测试场景: {scenario_name}")
            
            # 设置水印类型
            watermark_btn = getattr(window.ui, watermark_button)
            watermark_btn.setChecked(True)
            
            # 设置AI标识
            window.ui.checkBox_AI.setChecked(ai_enabled)
            
            # 验证设置
            watermark_selected = watermark_btn.isChecked()
            ai_selected = window.ui.checkBox_AI.isChecked()
            
            print(f"     水印类型: {'选中' if watermark_selected else '未选中'}")
            print(f"     AI标识: {'勾选' if ai_selected else '未勾选'}")
            
            if watermark_selected and (ai_selected == ai_enabled):
                print(f"     ✅ {scenario_name} 设置正确")
            else:
                print(f"     ❌ {scenario_name} 设置错误")
                return False
        
        # 5. 测试代码逻辑完整性
        print("\n5. 测试代码逻辑完整性:")
        
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查所有水印类型的处理逻辑
        watermark_checks = [
            ('SY_radioButton_nothing.isChecked()', '无水印检查'),
            ('SY_radioButton_HHLOGO_2.isChecked()', '动态LOGO检查'),
            ('SY_radioButton_HHLOGO.isChecked()', '全屏LOGO检查'),
            ('SY_radioButton_HHTPSJ.isChecked()', '预设图片检查'),
            ('SY_radioButton_HHlogoJB.isChecked()', '角标LOGO检查'),
            ('SY_radioButton_ZD.isChecked()', '自定义检查'),
            ('checkBox_AI.isChecked()', 'AI标识检查'),
        ]
        
        all_checks_found = True
        for check_pattern, description in watermark_checks:
            if check_pattern in content:
                print(f"   ✅ {description}: 找到相关代码")
            else:
                print(f"   ❌ {description}: 未找到相关代码")
                all_checks_found = False
        
        if not all_checks_found:
            return False
        
        # 6. 测试处理流程逻辑
        print("\n6. 测试处理流程逻辑:")
        
        flow_checks = [
            ('无水印模式：直接复制原文件', '无水印处理'),
            ('shutil.copy2(input_video, output_video)', '文件复制'),
            ('🤖 添加AI标识水印', 'AI标识处理'),
            ('get_photo_path("AI生成.png")', 'AI图片路径'),
            ('1.0  # 100%不透明度', 'AI标识不透明度'),
        ]
        
        all_flows_found = True
        for flow_pattern, description in flow_checks:
            if flow_pattern in content:
                print(f"   ✅ {description}: 找到相关代码")
            else:
                print(f"   ❌ {description}: 未找到相关代码")
                all_flows_found = False
        
        if not all_flows_found:
            return False
        
        # 7. 测试资源文件
        print("\n7. 测试资源文件:")
        
        from resource_manager import get_photo_path
        
        required_resources = [
            ('AI生成.png', 'AI标识图片'),
            ('HHlogo.png', '动态LOGO图片'),
            ('HHlogo整张水印.png', '全屏LOGO图片'),
        ]
        
        all_resources_exist = True
        for filename, description in required_resources:
            file_path = get_photo_path(filename)
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"   ✅ {description}: 存在，大小={file_size}字节")
            else:
                print(f"   ❌ {description}: 不存在")
                all_resources_exist = False
        
        if not all_resources_exist:
            return False
        
        print("\n🎉 完整水印系统测试通过！所有功能正常。")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")
        return False

def show_system_summary():
    """显示系统总结"""
    print("\n" + "="*70)
    print("完整水印系统功能总结")
    print("="*70)
    print()
    print("🎯 支持的水印类型:")
    print("   1. 无 - 不添加任何水印（新增）")
    print("   2. 动态合合LOGO - 动态移动的logo水印")
    print("   3. 全屏合合LOGO - 全屏图片水印")
    print("   4. 预设全屏图片随机 - 随机选择预设图片")
    print("   5. 合合LOGO角标 - 固定位置的logo水印")
    print("   6. 自定义 - 用户指定文件夹的图片")
    print()
    print("🤖 AI标识功能:")
    print("   - 可与任意水印类型组合使用")
    print("   - 100%不透明度，全屏覆盖")
    print("   - 在所有其他水印处理完成后添加")
    print()
    print("🔄 处理流程:")
    print("   原视频 → [水印处理] → [AI标识处理] → 最终输出")
    print()
    print("   具体流程:")
    print("   - 选择【无】: 直接复制 → [可选AI标识] → 输出")
    print("   - 选择其他: 添加水印 → [可选AI标识] → 输出")
    print()
    print("💡 使用场景:")
    print("   - 仅AI标识: 选择【无】+ 勾选AI标识")
    print("   - 仅水印: 选择水印类型 + 不勾选AI标识")
    print("   - 水印+AI: 选择水印类型 + 勾选AI标识")
    print("   - 完全不处理: 选择【无】+ 不勾选AI标识")
    print()

def main():
    """主函数"""
    print("完整水印系统测试工具")
    print("="*50)
    
    # 运行完整测试
    success = test_complete_watermark_system()
    
    # 显示系统总结
    show_system_summary()
    
    if success:
        print("🎉 完整水印系统实现完成！所有功能都可以正常使用。")
    else:
        print("⚠️ 发现问题，请检查上述测试输出。")

if __name__ == "__main__":
    main()
