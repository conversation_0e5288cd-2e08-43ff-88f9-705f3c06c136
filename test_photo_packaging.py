#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试photo文件夹打包情况
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_resource_paths():
    """测试资源路径"""
    print("=== 测试资源路径 ===")
    
    try:
        from resource_manager import (
            get_photo_path, 
            get_photo_folder_path, 
            get_resource_path,
            check_photo_resources
        )
        
        print("✅ 资源管理模块导入成功")
        
        # 检查基础路径
        base_path = get_resource_path("")
        print(f"基础路径: {base_path}")
        
        # 检查是否在打包环境中
        if hasattr(sys, '_MEIPASS'):
            print(f"✅ 检测到打包环境: {sys._MEIPASS}")
        else:
            print("📝 开发环境")
        
        # 检查photo路径
        photo_dir = get_photo_folder_path()
        print(f"Photo文件夹路径: {photo_dir}")
        print(f"Photo文件夹存在: {os.path.exists(photo_dir)}")
        
        # 检查具体文件
        logo_path = get_photo_path("HHlogo.png")
        watermark_path = get_photo_path("HHlogo整张水印.png")
        others_path = get_photo_folder_path("others")
        
        print(f"Logo路径: {logo_path}")
        print(f"Logo存在: {os.path.exists(logo_path)}")
        
        print(f"水印路径: {watermark_path}")
        print(f"水印存在: {os.path.exists(watermark_path)}")
        
        print(f"Others路径: {others_path}")
        print(f"Others存在: {os.path.exists(others_path)}")
        
        # 如果others存在，列出其中的文件
        if os.path.exists(others_path):
            try:
                others_files = os.listdir(others_path)
                image_files = [f for f in others_files 
                             if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'))]
                print(f"Others中的图片数量: {len(image_files)}")
                if len(image_files) > 0:
                    print(f"前5个图片: {image_files[:5]}")
            except Exception as e:
                print(f"❌ 无法列出others文件: {e}")
        
        # 使用check_photo_resources检查
        print("\n=== 使用check_photo_resources检查 ===")
        resources = check_photo_resources()
        for name, info in resources.items():
            status = "✅ 存在" if info["exists"] else "❌ 缺失"
            print(f"{name}: {status}")
            print(f"  路径: {info['path']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_paths():
    """直接测试路径"""
    print("\n=== 直接测试路径 ===")
    
    # 检查当前工作目录
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    print(f"脚本目录: {script_dir}")
    
    # 检查是否有_MEIPASS
    if hasattr(sys, '_MEIPASS'):
        meipass = sys._MEIPASS
        print(f"_MEIPASS: {meipass}")
        
        # 检查_MEIPASS中的photo
        photo_in_meipass = os.path.join(meipass, "photo")
        print(f"_MEIPASS中的photo: {photo_in_meipass}")
        print(f"存在: {os.path.exists(photo_in_meipass)}")
        
        if os.path.exists(photo_in_meipass):
            try:
                contents = os.listdir(photo_in_meipass)
                print(f"photo内容: {contents}")
            except Exception as e:
                print(f"❌ 无法列出photo内容: {e}")
    
    # 检查相对路径的photo
    relative_photo = os.path.join(script_dir, "photo")
    print(f"相对路径photo: {relative_photo}")
    print(f"存在: {os.path.exists(relative_photo)}")
    
    return True

def test_manual_path_construction():
    """手动构造路径测试"""
    print("\n=== 手动构造路径测试 ===")
    
    try:
        # 模拟resource_manager的逻辑
        try:
            base_path = sys._MEIPASS
            print(f"使用_MEIPASS: {base_path}")
        except AttributeError:
            base_path = os.path.dirname(os.path.abspath(__file__))
            print(f"使用脚本目录: {base_path}")
        
        # 构造photo路径
        photo_path = os.path.join(base_path, "photo")
        logo_path = os.path.join(photo_path, "HHlogo.png")
        watermark_path = os.path.join(photo_path, "HHlogo整张水印.png")
        others_path = os.path.join(photo_path, "others")
        
        print(f"Photo路径: {photo_path}")
        print(f"Photo存在: {os.path.exists(photo_path)}")
        
        print(f"Logo路径: {logo_path}")
        print(f"Logo存在: {os.path.exists(logo_path)}")
        
        print(f"水印路径: {watermark_path}")
        print(f"水印存在: {os.path.exists(watermark_path)}")
        
        print(f"Others路径: {others_path}")
        print(f"Others存在: {os.path.exists(others_path)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== Photo文件夹打包测试 ===")
    print("检查photo文件夹是否正确打包到exe中")
    print()
    
    # 测试资源路径
    resource_ok = test_resource_paths()
    
    # 直接测试路径
    direct_ok = test_direct_paths()
    
    # 手动构造路径测试
    manual_ok = test_manual_path_construction()
    
    print("\n=== 测试结果 ===")
    if resource_ok and direct_ok and manual_ok:
        print("✅ 路径测试通过")
        print("💡 如果在打包后的exe中运行此测试，应该能看到_MEIPASS路径")
        print("💡 如果photo文件夹正确打包，所有文件都应该存在")
        return True
    else:
        print("❌ 部分测试失败")
        print("💡 可能的问题:")
        print("   1. photo文件夹没有正确添加到spec文件")
        print("   2. 打包时photo文件夹不存在")
        print("   3. 路径构造逻辑有问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
