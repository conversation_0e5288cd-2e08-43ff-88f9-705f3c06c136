#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证photo文件夹是否正确打包到exe中
将此文件与exe放在同一目录下运行
"""

import sys
import os

def check_photo_in_exe():
    """检查exe中的photo文件夹"""
    print("=== 验证photo文件夹打包情况 ===")
    print()
    
    # 检查是否在打包环境中
    if hasattr(sys, '_MEIPASS'):
        print(f"✅ 检测到打包环境")
        print(f"临时目录: {sys._MEIPASS}")
        
        # 检查photo文件夹
        photo_path = os.path.join(sys._MEIPASS, "photo")
        print(f"Photo路径: {photo_path}")
        
        if os.path.exists(photo_path):
            print("✅ photo文件夹存在")
            
            # 检查文件夹内容
            try:
                contents = os.listdir(photo_path)
                print(f"Photo内容: {contents}")
                
                # 检查必需文件
                required_files = ["HHlogo.png", "HHlogo整张水印.png"]
                for file in required_files:
                    file_path = os.path.join(photo_path, file)
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        print(f"✅ {file} (大小: {file_size} 字节)")
                    else:
                        print(f"❌ {file} 不存在")
                
                # 检查others文件夹
                others_path = os.path.join(photo_path, "others")
                if os.path.exists(others_path):
                    others_files = os.listdir(others_path)
                    image_files = [f for f in others_files 
                                 if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp'))]
                    print(f"✅ others文件夹 ({len(image_files)} 张图片)")
                    if len(image_files) > 0:
                        print(f"前3张图片: {image_files[:3]}")
                else:
                    print("❌ others文件夹不存在")
                
            except Exception as e:
                print(f"❌ 无法读取photo内容: {e}")
        else:
            print("❌ photo文件夹不存在")
            
            # 列出_MEIPASS中的所有内容
            try:
                meipass_contents = os.listdir(sys._MEIPASS)
                print(f"_MEIPASS中的内容: {meipass_contents}")
            except Exception as e:
                print(f"❌ 无法列出_MEIPASS内容: {e}")
    else:
        print("❌ 未检测到打包环境（可能在开发环境中运行）")
        
        # 在开发环境中检查
        script_dir = os.path.dirname(os.path.abspath(__file__))
        photo_path = os.path.join(script_dir, "photo")
        
        if os.path.exists(photo_path):
            print(f"✅ 开发环境中找到photo文件夹: {photo_path}")
        else:
            print(f"❌ 开发环境中也没有photo文件夹: {photo_path}")

def main():
    """主函数"""
    print("Photo文件夹打包验证工具")
    print("=" * 50)
    
    check_photo_in_exe()
    
    print("\n" + "=" * 50)
    print("验证完成")
    
    # 等待用户按键
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
