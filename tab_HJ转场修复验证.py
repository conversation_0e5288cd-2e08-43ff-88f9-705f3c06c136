#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
tab_HJ转场功能修复验证脚本
检查转场功能的关键修复是否正确应用
"""

import os
import sys
from pathlib import Path

def test_transition_mixer_fixes():
    """测试transition_mixer.py中的修复"""
    print("🔍 测试一：transition_mixer.py修复验证")
    
    try:
        with open('transition_mixer.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        fixes_found = []
        
        # 检查音频混合normalize参数修复
        if "amix=inputs=2:duration=longest:normalize=0[outa]" in content:
            fixes_found.append("✅ 音频混合normalize参数已修复")
        else:
            fixes_found.append("❌ 音频混合normalize参数未修复")
            
        # 检查音频延迟计算修复
        if "delay_ms = max(0, int(offset * 1000))" in content:
            fixes_found.append("✅ 音频延迟计算已修复")
        else:
            fixes_found.append("❌ 音频延迟计算未修复")
            
        # 检查转场时长验证修复
        if "max_safe_duration = min(main_duration * 0.3, variant_duration * 0.3)" in content:
            fixes_found.append("✅ 转场时长验证已修复")
        else:
            fixes_found.append("❌ 转场时长验证未修复")
            
        # 检查滤镜复杂度阈值修复
        if "if len(filter_complex) > 600:" in content:
            fixes_found.append("✅ 滤镜复杂度阈值已修复")
        else:
            fixes_found.append("❌ 滤镜复杂度阈值未修复")
            
        # 检查错误回退机制
        if "return self._fallback_simple_concat(video1, video2, output_path, mute_original)" in content:
            fixes_found.append("✅ 错误回退机制已添加")
        else:
            fixes_found.append("❌ 错误回退机制未添加")
            
        for fix in fixes_found:
            print(fix)
            
        return all("✅" in fix for fix in fixes_found)
        
    except Exception as e:
        print(f"❌ 读取transition_mixer.py失败：{e}")
        return False

def test_main_py_fixes():
    """测试main.py中的修复"""
    print("\n🔍 测试二：main.py转场逻辑修复验证")
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        fixes_found = []
        
        # 检查转场设置验证
        if "验证转场设置" in content and "转场效果解析失败" in content:
            fixes_found.append("✅ 转场设置验证已添加")
        else:
            fixes_found.append("❌ 转场设置验证未添加")
            
        # 检查调试信息增强
        if "🎬 转场类型:" in content and "🎬 视频文件数量:" in content:
            fixes_found.append("✅ 调试信息已增强")
        else:
            fixes_found.append("❌ 调试信息未增强")
            
        # 检查随机转场日志
        if "🎲 全随机转场" in content and "🎲 柔和随机转场" in content:
            fixes_found.append("✅ 随机转场日志已添加")
        else:
            fixes_found.append("❌ 随机转场日志未添加")
            
        for fix in fixes_found:
            print(fix)
            
        return all("✅" in fix for fix in fixes_found)
        
    except Exception as e:
        print(f"❌ 读取main.py失败：{e}")
        return False

def test_audio_crackling_fixes():
    """测试音频呲呲啦啦声音修复"""
    print("\n🔍 测试三：音频呲呲啦啦声音修复验证")
    
    try:
        with open('transition_mixer.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        fixes_found = []
        
        # 检查音频编码参数
        if '"256k"' in content and '"48000"' in content and '"-b:a"' in content and '"-ar"' in content:
            fixes_found.append("✅ 音频编码参数已优化")
        else:
            fixes_found.append("❌ 音频编码参数未优化")
            
        # 检查normalize参数
        if ":normalize=0[outa]" in content:
            fixes_found.append("✅ 音频normalize参数已修复")
        else:
            fixes_found.append("❌ 音频normalize参数未修复")
            
        # 检查音频延迟处理
        if "delay_ms = max(0" in content:
            fixes_found.append("✅ 音频延迟处理已优化")
        else:
            fixes_found.append("❌ 音频延迟处理未优化")
            
        for fix in fixes_found:
            print(fix)
            
        return all("✅" in fix for fix in fixes_found)
        
    except Exception as e:
        print(f"❌ 检查音频修复失败：{e}")
        return False

def test_error_handling():
    """测试错误处理机制"""
    print("\n🔍 测试四：错误处理机制验证")
    
    try:
        with open('transition_mixer.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        fixes_found = []
        
        # 检查异常回退机制
        if "转场失败，自动回退到简单拼接" in content:
            fixes_found.append("✅ 异常回退机制已添加")
        else:
            fixes_found.append("❌ 异常回退机制未添加")
            
        # 检查滤镜复杂度检查
        if "滤镜过于复杂" in content and "直接使用简单拼接" in content:
            fixes_found.append("✅ 滤镜复杂度检查已添加")
        else:
            fixes_found.append("❌ 滤镜复杂度检查未添加")
            
        # 检查转场时长验证
        if "确保转场时长至少为0.1秒，最多为5秒" in content:
            fixes_found.append("✅ 转场时长验证已添加")
        else:
            fixes_found.append("❌ 转场时长验证未添加")
            
        for fix in fixes_found:
            print(fix)
            
        return all("✅" in fix for fix in fixes_found)
        
    except Exception as e:
        print(f"❌ 检查错误处理失败：{e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 tab_HJ转场功能修复验证测试")
    print("=" * 60)
    
    tests = [
        test_transition_mixer_fixes,
        test_main_py_fixes,
        test_audio_crackling_fixes,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有修复验证通过！tab_HJ转场功能应该已经修复")
        print("\n📝 修复总结:")
        print("✅ 音频呲呲啦啦声音问题已修复")
        print("✅ 转场时长计算问题已修复")
        print("✅ 音频延迟同步问题已修复")
        print("✅ 滤镜复杂度检查已优化")
        print("✅ 错误处理和回退机制已完善")
        print("✅ 调试信息已增强")
        print("\n💡 建议:")
        print("1. 重新启动应用程序")
        print("2. 测试转场功能，特别是音频效果")
        print("3. 如果仍有问题，请查看日志中的详细错误信息")
    else:
        print("⚠️ 部分修复未通过验证，请检查相关文件")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
