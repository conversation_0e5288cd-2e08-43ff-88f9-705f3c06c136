from pathlib import Path
from PySide6.QtCore import QThread, Signal
import subprocess
import re
import random
import sys
from typing import List, Dict, Tuple

if sys.platform.startswith('win'):
    # 隐藏控制台窗口的标志
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0

# 导入全局设置
try:
    from main import VIDEO_BITRATE, GPU_ENABLED, CPU_PRESET, GPU_PRESET, is_gpu_supported, ENABLE_VIDEO_PREPROCESSING
except ImportError:
    # 如果无法导入，使用默认值
    VIDEO_BITRATE = "15000k"
    GPU_ENABLED = True
    CPU_PRESET = "slow"
    GPU_PRESET = "p2"
    ENABLE_VIDEO_PREPROCESSING = True

    def is_gpu_supported():
        return True


class VideoMixer:
    def __init__(self):
        self.ffmpeg_path = None
        self.ffprobe_path = None
        self.main_files: List[Path] = []
        self.variant_files: List[Path] = []
        self.output_dir: Path = Path("output")
        self.k = 3  # 分组数，默认值
        self.strategies: List[Tuple[str, int]] = []
        self.selected_strategy: str = ""
        self.log_callback = None
        self._is_running = False
        self.transition_config = None  # 转场配置

        # 查找 FFmpeg 路径
        if not self.find_ffmpeg():
            raise RuntimeError("未找到 FFmpeg 或 FFprobe")

    def set_log_callback(self, callback):
        self.log_callback = callback

    def set_loudnorm_enabled(self, enabled):
        """设置是否启用响度统一"""
        self.enable_loudnorm = enabled
        if enabled:
            self._log("🔊 已启用响度统一功能")
        else:
            self._log("🔇 已禁用响度统一功能")

    def _log(self, message: str):
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def _try_gpu_encode(self, cmd_base, output_file):
        """尝试GPU编码，失败时返回False"""
        try:
            # 导入主程序的GPU函数
            from main import build_gpu_ffmpeg_cmd

            # 使用统一的GPU加速函数
            gpu_cmd, using_gpu = build_gpu_ffmpeg_cmd(cmd_base, force_gpu=True)

            if not using_gpu:
                self._log("❌ GPU不可用，跳过GPU编码")
                return False

            self._log(f"🚀 GPU编码中...")

            result = subprocess.run(
                gpu_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                timeout=3600,
                creationflags=CREATE_NO_WINDOW
            )

            if result.returncode == 0 and output_file.exists() and output_file.stat().st_size > 0:
                self._log(f"✅ GPU加速编码成功")
                return True
            else:
                # 特殊处理Windows内存访问错误
                if result.returncode == 4294967274:
                    self._log("❌ GPU编码失败: FFmpeg内存访问错误，可能是滤镜过于复杂")
                else:
                    self._log(f"❌ GPU编码失败: {result.stderr[:200] if result.stderr else '未知错误'}")
                # 删除可能生成的不完整文件
                if output_file.exists():
                    output_file.unlink()
                return False

        except Exception as e:
            self._log(f"❌ GPU编码异常: {str(e)[:100]}")
            # 删除可能生成的不完整文件
            if output_file.exists():
                output_file.unlink()
            return False

    def _fallback_cpu_encode(self, cmd_base, output_file):
        """CPU编码回退方案"""
        try:
            # 构建CPU编码命令
            cpu_cmd = cmd_base.copy()

            # 找到视频编码器位置并替换为CPU编码器
            if "-c:v" in cpu_cmd:
                codec_index = cpu_cmd.index("-c:v") + 1
                cpu_cmd[codec_index] = "libx264"
            else:
                cpu_cmd.extend(["-c:v", "libx264"])

            # 添加CPU相关参数（不使用CRF，保持码率控制）
            cpu_cmd.extend(["-preset", CPU_PRESET])

            self._log(f"🔄 CPU编码中...")

            result = subprocess.run(
                cpu_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                timeout=3600,
                creationflags=CREATE_NO_WINDOW
            )

            if result.returncode == 0 and output_file.exists() and output_file.stat().st_size > 0:
                self._log(f"✅ CPU编码成功")
                return True
            else:
                # 特殊处理Windows内存访问错误
                if result.returncode == 4294967274:
                    self._log("❌ CPU编码也失败: FFmpeg内存访问错误，可能是滤镜过于复杂")
                else:
                    self._log(f"❌ CPU编码也失败: {result.stderr[:200] if result.stderr else '未知错误'}")
                return False

        except Exception as e:
            self._log(f"❌ CPU编码异常: {str(e)[:100]}")
            return False

    def _preprocess_video_if_needed(self, video_file: Path) -> Path:
        """预处理视频文件，确保编码兼容性"""
        try:
            # 检查视频是否需要预处理
            if self._is_video_compatible(video_file):
                return video_file

            # 创建预处理后的文件路径
            temp_dir = video_file.parent / "temp_processed"
            temp_dir.mkdir(exist_ok=True)
            processed_file = temp_dir / f"processed_{video_file.name}"

            # 如果已经存在预处理文件，直接返回
            if processed_file.exists() and processed_file.stat().st_size > 0:
                self._log(f"🔄 使用已预处理文件: {processed_file.name}")
                return processed_file

            self._log(f"🔧 预处理视频文件: {video_file.name}")

            # 构建标准化命令
            cmd = [
                str(self.ffmpeg_path),
                "-i", str(video_file),
                "-c:v", "libx264",  # 强制使用H.264编码
                "-preset", "medium",  # 平衡速度和质量
                "-crf", "23",  # 恒定质量模式
                "-pix_fmt", "yuv420p",  # 标准像素格式
                "-r", "30",  # 标准帧率
                "-c:a", "aac",  # 标准音频编码
                "-ar", "48000",  # 标准音频采样率
                "-ac", "2",  # 双声道
                "-b:a", "192k",  # 音频码率
                "-movflags", "+faststart",  # 优化MP4结构
                "-avoid_negative_ts", "make_zero",  # 修复时间戳问题
                "-fflags", "+genpts",  # 重新生成时间戳
                "-y",
                str(processed_file)
            ]

            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                timeout=300,  # 5分钟超时
                creationflags=CREATE_NO_WINDOW
            )

            if result.returncode == 0 and processed_file.exists() and processed_file.stat().st_size > 0:
                self._log(f"✅ 视频预处理成功: {processed_file.name}")
                return processed_file
            else:
                self._log(f"❌ 视频预处理失败: {result.stderr[:200] if result.stderr else '未知错误'}")
                # 删除失败的文件
                if processed_file.exists():
                    processed_file.unlink()
                return video_file

        except Exception as e:
            self._log(f"❌ 视频预处理异常: {str(e)[:100]}")
            return video_file

    def _is_video_compatible(self, video_file: Path) -> bool:
        """检查视频是否兼容，避免不必要的预处理"""
        try:
            cmd = [
                str(self.ffprobe_path),
                "-v", "error",
                "-select_streams", "v:0",
                "-show_entries", "stream=codec_name,pix_fmt,r_frame_rate",
                "-select_streams", "a:0",
                "-show_entries", "stream=codec_name,sample_rate,channels",
                "-of", "csv=p=0",
                str(video_file)
            ]

            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                timeout=10,
                creationflags=CREATE_NO_WINDOW
            )

            if result.returncode != 0:
                return False

            lines = result.stdout.strip().split('\n')
            if len(lines) < 1:
                return False

            # 检查视频流
            video_info = lines[0].split(',')
            if len(video_info) >= 3:
                codec, pix_fmt, frame_rate = video_info[:3]
                # 检查是否为标准H.264编码和像素格式
                if codec != 'h264' or pix_fmt != 'yuv420p':
                    return False

            # 检查音频流（如果存在）
            if len(lines) > 1:
                audio_info = lines[1].split(',')
                if len(audio_info) >= 3:
                    codec, sample_rate, channels = audio_info[:3]
                    # 检查是否为标准AAC编码
                    if codec != 'aac' or sample_rate != '48000' or channels != '2':
                        return False

            return True

        except Exception:
            # 如果检查失败，假设需要预处理
            return False

    def _cleanup_temp_files(self, processed_main: Path, processed_variant: Path,
                           original_main: Path, original_variant: Path):
        """清理临时预处理文件"""
        try:
            # 只删除预处理生成的临时文件
            if processed_main != original_main and processed_main.exists():
                if "temp_processed" in str(processed_main):
                    processed_main.unlink()

            if processed_variant != original_variant and processed_variant.exists():
                if "temp_processed" in str(processed_variant):
                    processed_variant.unlink()

            # 尝试删除空的临时目录
            for original_file in [original_main, original_variant]:
                temp_dir = original_file.parent / "temp_processed"
                if temp_dir.exists() and not any(temp_dir.iterdir()):
                    temp_dir.rmdir()

        except Exception as e:
            # 清理失败不影响主流程
            pass

    def find_ffmpeg(self, predefined_paths=None):
        """查找 FFmpeg 和 FFprobe 路径"""
        if predefined_paths is None:
            predefined_paths = [
                Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/"),
                Path("C:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/"),
                Path("E:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/"),
                Path("F:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/")
            ]

        for path in predefined_paths:
            ffmpeg_exe = path / "ffmpeg.exe"
            ffprobe_exe = path / "ffprobe.exe"
            if ffmpeg_exe.exists() and ffprobe_exe.exists():
                self.ffmpeg_path = ffmpeg_exe
                self.ffprobe_path = ffprobe_exe
                self._log(f"找到 FFmpeg: {ffmpeg_exe}")
                return True

        self._log("❌ 未找到 FFmpeg 或 FFprobe")
        return False

    def validate_files(self, files: List[Path], name: str) -> bool:
        """验证文件列表有效性"""
        invalid_files = [f for f in files if not f.exists() or f.suffix.lower() != ".mp4"]
        if invalid_files:
            self._log(f"❌ {name} 素材中存在无效文件:")
            for f in invalid_files:
                self._log(f"  - {f}")
            return False
        self._log(f"✅ {name} 素材验证通过: {len(files)} 个文件")
        return True

    def check_video_format(self, videos: List[Path], name: str) -> bool:
        """检查视频格式一致性"""
        resolutions = {}
        fps_values = {}

        for video in videos:
            info = self.get_video_info(video)
            if not info:
                continue

            resolution = f"{info['width']}x{info['height']}"
            fps = info['fps']

            resolutions[resolution] = resolutions.get(resolution, 0) + 1
            fps_values[fps] = fps_values.get(fps, 0) + 1

        # 检查分辨率一致性
        if len(resolutions) > 1:
            self._log(f"⚠️ {name} 素材分辨率不一致:")
            for res, count in resolutions.items():
                self._log(f"  - {res}: {count} 个")
            return False

        # 检查帧率一致性
        if len(fps_values) > 1:
            self._log(f"⚠️ {name} 素材帧率不一致:")
            for fps, count in fps_values.items():
                self._log(f"  - {fps}fps: {count} 个")
            return False

        self._log(f"✅ {name} 素材格式一致")
        return True

    def get_video_info(self, file_path: Path) -> Dict[str, float]:
        """获取视频信息"""
        cmd = [
            str(self.ffprobe_path), "-v", "error", "-select_streams", "v:0",
            "-show_entries", "stream=width,height,r_frame_rate",
            "-of", "csv=p=0", str(file_path)
        ]

        try:
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                timeout=10,
                creationflags=CREATE_NO_WINDOW  #隐藏黑色弹窗
            )
            output = result.stdout.strip()
            if not output:
                self._log(f"⚠️ 无法获取视频信息: {file_path}")
                return None

            width, height, frame_rate = output.split(',')
            fps = eval(frame_rate)  # 将分数转换为浮点数

            return {
                'width': int(width),
                'height': int(height),
                'fps': round(fps, 2)
            }

        except Exception as e:
            self._log(f"⚠️ 获取视频信息失败: {file_path} - {str(e)}")
            return None

    def determine_optimal_k(self, n1: int, n2: int) -> int:
        is_small1, is_small2 = n1 <= 10, n2 <= 10
        kmax1 = n1 if is_small1 else n1 // 2  # 小数据（≤10个）允许全部分组，大数据最多分一半
        kmax2 = n2 if is_small2 else n2 // 2
        kmax = min(kmax1, kmax2)  # 取两者的最小最大分组数

        if kmax < 3:  # 至少分3组
            self._log("❌ 无法满足组数≥3，请增加素材数量（至少3个/文件夹）")
            return 3

        common_factors = self.determine_common_factors(n1, n2)
        candidates = common_factors if common_factors else list(range(3, kmax + 1))  # 优先使用公共因数，否则顺序生成3到kmax的分组数

        best_k, min_diff = None, float('inf')
        for k in candidates:
            g1 = self._split(n1, k, is_small1)  # 拆分前贴素材为k组
            g2 = self._split(n2, k, is_small2)  # 拆分后贴素材为k组
            if not g1 or not g2:  # 确保分组有效（大数据组每组至少2个文件）
                continue
            current_diff = max(max(g1), max(g2)) - min(min(g1), min(g2))  # 计算分组后最大组与最小组的差异
            # 选择差异最小的k，差异相同时选择更大的k（更多分组）
            if current_diff < min_diff or (current_diff == min_diff and k > best_k):
                best_k, min_diff = k, current_diff
        return best_k if best_k is not None else 3

    def determine_common_factors(self, n1, n2, min_k=3):
        factors1 = set(i for i in range(min_k, n1 + 1) if n1 % i == 0)
        factors2 = set(i for i in range(min_k, n2 + 1) if n2 % i == 0)
        return sorted(factors1 & factors2, reverse=True)

    def _split(self, n, k, is_small):
        a, r = divmod(n, k)
        groups = [a + 1] * r + [a] * (k - r)  # 平均分配，余数在前（例如8个文件分4组：2,2,2,2）
        if not is_small and any(g < 2 for g in groups):  # 大数据（>10个）要求每组至少2个文件
            return None
        return groups

    def generate_strategies(self) -> List[Tuple[str, int]]:
        """生成可用的混剪策略"""
        n1 = len(self.main_files)
        n2 = len(self.variant_files)

        strategies = []

        # 一个前贴只用一次（置顶策略）- 不需要分组
        true_one_to_one_count = min(n1, n2)
        strategies.append(("一个前贴只用一次", true_one_to_one_count))

        # 一个前贴用两次后贴（新增策略）- 不需要分组
        if n2 >= 2:  # 至少需要2个后贴才能实现
            one_to_two_count = n1 * 2  # 每个前贴生成2个视频
            strategies.append(("一个前贴用两次后贴", one_to_two_count))

        # 如果素材数量足够，才进行分组和其他策略
        if n1 >= 3 and n2 >= 3:
            self.k = self.determine_optimal_k(n1, n2)
            self._log(f"📦 分组结果：k={self.k}组（强制≥3组）")

            main_groups = self._split_into_groups(self.main_files, self.k)
            variant_groups = self._split_into_groups(self.variant_files, self.k)
            m_lens = [len(g) for g in main_groups]
            v_lens = [len(g) for g in variant_groups]

            self._log(f"  前贴分组：{m_lens}（每组≥2，小数据允许单文件）")
            self._log(f"  后贴分组：{v_lens}（小数据允许单文件）")
        else:
            # 素材数量不足，只提供基础策略
            self._log(f"📦 素材数量不足分组（前贴:{n1}个，后贴:{n2}个），仅提供基础策略")
            self.k = 1  # 设置为1组
            m_lens = [n1]
            v_lens = [n2]

        # 全笛卡尔积
        cartesian_count = sum(m * v for m in m_lens for v in v_lens)
        strategies.append(("全笛卡尔积", cartesian_count))

        # 只有在素材数量足够时才提供其他策略
        if n1 >= 3 and n2 >= 3:
            # 一对一
            one_to_one_count = sum(m * v for m, v in zip(m_lens, v_lens))
            strategies.append(("一对一", one_to_one_count))

            # 一对多策略（最多到一对6组）
            max_pairs = min(self.k - 1, 6)
            for match_count in range(2, max_pairs + 1):
                total = 0
                for i in range(self.k):
                    v_indices = [(i + j) % self.k for j in range(match_count)]
                    v_total = sum(v_lens[j] for j in v_indices)
                    total += m_lens[i] * v_total
                strategies.append((f"一对{match_count}组", total))

        # 按生成数量排序（升序）
        strategies.sort(key=lambda x: x[1])
        return strategies

    def _split_into_groups(self, items: List[Path], k: int) -> List[List[Path]]:
        """将列表分成 k 个近似相等的组"""
        n = len(items)
        if k <= 0 or k > n:
            return [items]

        group_size = n // k
        remainder = n % k

        groups = []
        start = 0
        for i in range(k):
            size = group_size + (1 if i < remainder else 0)
            groups.append(items[start:start + size])
            start += size

        return groups

    def merge_videos(self):
        """执行视频混剪"""
        self._is_running = True
        self._log("\n开始混剪任务...")

        if not self.selected_strategy:
            self._log("❌ 未选择混剪策略")
            return

        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 根据选择的策略执行混剪
        if self.selected_strategy == "一个前贴只用一次":
            self._merge_true_one_to_one()
        elif self.selected_strategy == "一个前贴用两次后贴":
            self._merge_one_to_two()
        elif self.selected_strategy == "全笛卡尔积":
            self._merge_cartesian_product()
        elif self.selected_strategy == "一对一":
            self._merge_one_to_one()
        elif "一对" in self.selected_strategy:
            match_count = int(re.search(r'一对(\d+)组', self.selected_strategy).group(1))
            self._merge_one_to_many(match_count)
        else:
            self._log(f"❌ 不支持的策略: {self.selected_strategy}")

        self._is_running = False
        self._log("混剪任务完成!")

    def _merge_true_one_to_one(self):
        """执行一个前贴只用一次混剪"""
        main_count = len(self.main_files)
        variant_count = len(self.variant_files)
        total = min(main_count, variant_count)

        self._log(f"🔄 使用一个前贴只用一次策略: {main_count} 前贴 x {variant_count} 后贴 → {total} 个视频")

        # 如果后贴数量大于等于前贴数量，确保后贴选择不重复
        if variant_count >= main_count:
            # 随机选择不重复的后贴
            selected_variants = random.sample(self.variant_files, main_count)
            variant_files_to_use = selected_variants
        else:
            # 后贴数量小于前贴数量，可以重复选择后贴
            variant_files_to_use = []
            for i in range(main_count):
                variant_files_to_use.append(random.choice(self.variant_files))

        count = 0
        for i, main_file in enumerate(self.main_files):
            if not self._is_running:
                break

            if i >= len(variant_files_to_use):
                break

            variant_file = variant_files_to_use[i]
            output_file = self.output_dir / f"one_front_once_{i + 1}.mp4"
            self._merge_two_videos(main_file, variant_file, output_file, self.transition_config)
            count += 1
            self._log(f"进度: {count}/{total} - 已生成: {output_file.name}")

    def _merge_one_to_two(self):
        """执行一个前贴用两次后贴混剪"""
        main_count = len(self.main_files)
        variant_count = len(self.variant_files)
        total = main_count * 2  # 每个前贴生成2个视频

        self._log(f"🔄 使用一个前贴用两次后贴策略: {main_count} 前贴 x {variant_count} 后贴 → {total} 个视频")

        # 为每个前贴选择两个后贴
        variant_pairs = []

        if variant_count >= main_count * 2:
            # 后贴数量充足，确保不重复选择
            self._log("后贴数量充足，采用不重复选择策略")
            selected_variants = random.sample(self.variant_files, main_count * 2)
            for i in range(main_count):
                variant_pairs.append([selected_variants[i * 2], selected_variants[i * 2 + 1]])
        else:
            # 后贴数量不足，采用平均分配策略
            self._log("后贴数量不足，采用平均分配策略")
            # 创建使用频次计数器
            usage_count = {variant: 0 for variant in self.variant_files}

            for i in range(main_count):
                # 为当前前贴选择两个不同的后贴
                pair = []

                # 选择第一个后贴（使用频次最少的）
                min_usage = min(usage_count.values())
                candidates = [v for v, count in usage_count.items() if count == min_usage]
                first_variant = random.choice(candidates)
                pair.append(first_variant)
                usage_count[first_variant] += 1

                # 选择第二个后贴（使用频次最少且不同于第一个的）
                if len(self.variant_files) > 1:
                    min_usage = min(usage_count.values())
                    candidates = [v for v, count in usage_count.items()
                                if count == min_usage and v != first_variant]
                    if not candidates:
                        # 如果没有不同的候选者，从所有不同于第一个的后贴中选择使用频次最少的
                        min_usage = min(count for v, count in usage_count.items() if v != first_variant)
                        candidates = [v for v, count in usage_count.items()
                                    if count == min_usage and v != first_variant]
                    second_variant = random.choice(candidates)
                    pair.append(second_variant)
                    usage_count[second_variant] += 1
                else:
                    # 只有一个后贴的情况，重复使用
                    pair.append(first_variant)
                    usage_count[first_variant] += 1

                variant_pairs.append(pair)

        # 执行混剪
        count = 0
        for i, main_file in enumerate(self.main_files):
            if not self._is_running:
                break

            variant_pair = variant_pairs[i]

            # 生成第一个视频
            output_file1 = self.output_dir / f"one_to_two_{i + 1}_1.mp4"
            self._merge_two_videos(main_file, variant_pair[0], output_file1, self.transition_config)
            count += 1
            self._log(f"进度: {count}/{total} - 已生成: {output_file1.name}")

            if not self._is_running:
                break

            # 生成第二个视频
            output_file2 = self.output_dir / f"one_to_two_{i + 1}_2.mp4"
            self._merge_two_videos(main_file, variant_pair[1], output_file2, self.transition_config)
            count += 1
            self._log(f"进度: {count}/{total} - 已生成: {output_file2.name}")

    def _merge_cartesian_product(self):
        """执行全笛卡尔积混剪"""
        self._log(f"🔄 使用全笛卡尔积策略: {len(self.main_files)} x {len(self.variant_files)}")

        count = 0
        total = len(self.main_files) * len(self.variant_files)

        for i, main_file in enumerate(self.main_files):
            if not self._is_running:
                break

            for j, variant_file in enumerate(self.variant_files):
                if not self._is_running:
                    break

                output_file = self.output_dir / f"merged_{i + 1}_{j + 1}.mp4"
                self._merge_two_videos(main_file, variant_file, output_file, self.transition_config)
                count += 1
                self._log(f"进度: {count}/{total} - 已生成: {output_file.name}")

    def _merge_one_to_one(self):
        """执行一对一混剪"""
        main_groups = self._split_into_groups(self.main_files, self.k)
        variant_groups = self._split_into_groups(self.variant_files, self.k)

        count = 0
        total = sum(len(mg) * len(vg) for mg, vg in zip(main_groups, variant_groups))

        for group_idx, (main_group, variant_group) in enumerate(zip(main_groups, variant_groups)):
            if not self._is_running:
                break

            for i, main_file in enumerate(main_group):
                if not self._is_running:
                    break

                for j, variant_file in enumerate(variant_group):
                    if not self._is_running:
                        break

                    output_file = self.output_dir / f"merged_{group_idx + 1}_{i + 1}_{j + 1}.mp4"
                    self._merge_two_videos(main_file, variant_file, output_file, self.transition_config)
                    count += 1
                    self._log(f"进度: {count}/{total} - 已生成: {output_file.name}")

    def _merge_one_to_many(self, match_count):
        """执行一对多混剪"""
        main_groups = self._split_into_groups(self.main_files, self.k)
        variant_groups = self._split_into_groups(self.variant_files, self.k)

        count = 0
        total = 0
        for i in range(self.k):
            v_indices = [(i + j) % self.k for j in range(match_count)]
            v_total = sum(len(variant_groups[j]) for j in v_indices)
            total += len(main_groups[i]) * v_total

        self._log(f"🔄 使用一对{match_count}组策略")

        for i in range(self.k):
            if not self._is_running:
                break

            v_indices = [(i + j) % self.k for j in range(match_count)]
            for v_idx in v_indices:
                if not self._is_running:
                    break

                for main_file in main_groups[i]:
                    if not self._is_running:
                        break

                    for variant_file in variant_groups[v_idx]:
                        if not self._is_running:
                            break

                        output_file = self.output_dir / f"one_to_{match_count}_{i + 1}_{v_idx + 1}_{count + 1}.mp4"
                        self._merge_two_videos(main_file, variant_file, output_file, self.transition_config)
                        count += 1
                        self._log(f"进度: {count}/{total} - 已生成: {output_file.name}")



    def _merge_two_videos(self, main_file: Path, variant_file: Path, output_file: Path, transition_config=None):
        """合并两个视频文件，支持转场效果"""
        if not self._is_running:
            return

        # 预处理视频文件，确保兼容性（如果启用）
        if ENABLE_VIDEO_PREPROCESSING:
            processed_main = self._preprocess_video_if_needed(main_file)
            processed_variant = self._preprocess_video_if_needed(variant_file)

            # 如果预处理失败，使用原文件
            if not processed_main:
                processed_main = main_file
            if not processed_variant:
                processed_variant = variant_file
        else:
            processed_main = main_file
            processed_variant = variant_file

        # 检测两个视频是否都有音频流
        has_audio_0 = self._has_audio_stream(processed_main)
        has_audio_1 = self._has_audio_stream(processed_variant)

        # 如果启用了转场功能
        if transition_config and transition_config.get('enabled', False):
            transition_type = transition_config.get('type', 'fade')
            transition_duration = transition_config.get('duration', 1.0)

            # 添加调试信息
            self._log(f"🎬 转场配置: {transition_config}")
            self._log(f"🎬 转场类型: {transition_type}")
            self._log(f"🎬 转场时长: {transition_duration}秒")

            # 处理随机转场 - 每个视频都重新随机选择
            if transition_type.startswith("【【") and "随机" in transition_type:
                import random
                if transition_type == "【【全随机】】":
                    # 从所有转场中随机选择
                    all_transitions = ["fade", "wipeleft", "wiperight", "wipeup", "wipedown",
                                     "smoothleft", "smoothright", "smoothup", "smoothdown",
                                     "slideleft", "slideright", "slideup", "slidedown", "circleopen",
                                     "circleclose", "vertopen", "vertclose", "horzopen", "horzclose"]
                    actual_transition = random.choice(all_transitions)
                elif transition_type == "【【柔 · 随机】】":
                    # 从柔和转场中随机选择
                    soft_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                                      "circleopen", "circleclose", "vertopen", "vertclose",
                                      "horzopen", "horzclose", "distance", "diagtl", "diagtr", "diagbl", "diagbr"]
                    actual_transition = random.choice(soft_transitions)
                elif transition_type == "【【硬 · 随机】】":
                    # 从硬切转场中随机选择
                    hard_transitions = ["wipeleft", "wiperight", "wipeup", "wipedown",
                                      "slideleft", "slideright", "slideup", "slidedown", "radial",
                                      "pixelize", "zoomin", "wipetl", "wipetr", "wipebl", "wipebr"]
                    actual_transition = random.choice(hard_transitions)
                else:
                    # 如果是其他类型，需要解析
                    actual_transition = self._parse_transition_type(transition_type)

                self._log(f"🎲 随机转场: {transition_type} → {actual_transition}")
                transition_type = actual_transition
            elif transition_type.startswith("【"):
                # 非随机的中文转场，需要解析
                transition_type = self._parse_transition_type(transition_type)

            # 获取第一个视频的分辨率作为目标分辨率
            try:
                from utils import get_video_metadata
                first_video_meta = get_video_metadata(processed_main)
                if first_video_meta and 'streams' in first_video_meta:
                    video_stream = first_video_meta['streams'][0]
                    target_width = video_stream.get('width', 1920)
                    target_height = video_stream.get('height', 1080)
                else:
                    target_width, target_height = 1920, 1080
            except:
                target_width, target_height = 1920, 1080

            # 获取视频时长来计算正确的offset
            try:
                from utils import get_video_duration
                main_duration = get_video_duration(processed_main)
                variant_duration = get_video_duration(processed_variant)

                if main_duration > 0 and variant_duration > 0:
                    # 确保转场时长不会超过前后视频的最短时长的50%
                    safe_duration = min(transition_duration, main_duration * 0.5, variant_duration * 0.5)
                    # 设置转场开始位置为前一个视频的末尾减去转场时长
                    offset = main_duration - safe_duration
                    self._log(f"🎬 视频时长: 前贴={main_duration:.2f}s, 后贴={variant_duration:.2f}s")
                    self._log(f"🎬 转场计算: 安全时长={safe_duration:.2f}s, offset={offset:.2f}s")
                else:
                    safe_duration = transition_duration
                    offset = 0
                    self._log(f"⚠️ 无法获取视频时长，使用默认转场参数")
            except:
                # 如果无法获取时长，使用默认值
                safe_duration = transition_duration
                offset = 0
                self._log(f"⚠️ 获取视频时长失败，使用默认转场参数")

            # 使用转场效果的滤镜 - 修复引号问题、分辨率问题和offset计算
            if has_audio_0 and has_audio_1:
                # 两个视频都有音频，实现音频转场：A音频淡出 + B音频淡入
                # 结构：A视频+A音频→AB视频转场+AB音频淡入淡出部分→B视频+B音频
                filter_complex = f"[0:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v0];"
                filter_complex += f"[1:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v1];"
                filter_complex += f"[v0][v1]xfade=transition='{transition_type}':duration={safe_duration:.3f}:offset={offset:.3f}[outv];"

                # 音频转场：先对每个音频流进行响度统一，再进行转场处理
                enable_loudnorm = getattr(self, 'enable_loudnorm', False)
                if enable_loudnorm:
                    # 启用响度统一：先统一响度，再进行转场
                    # 使用更稳定的响度统一参数，避免音量逐渐变大
                    filter_complex += f"[0:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[a0_norm];"
                    filter_complex += f"[1:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[a1_norm];"
                    filter_complex += f"[a0_norm]afade=t=out:st={offset:.3f}:d={safe_duration:.3f}[a0_fade];"
                    filter_complex += f"[a1_norm]adelay={int(offset*1000)}|{int(offset*1000)}[a1_delayed];"
                    filter_complex += f"[a1_delayed]afade=t=in:st={offset:.3f}:d={safe_duration:.3f}[a1_fade];"
                    filter_complex += f"[a0_fade][a1_fade]amix=inputs=2:duration=longest[outa]"
                else:
                    # 不启用响度统一：直接进行转场
                    filter_complex += f"[0:a]afade=t=out:st={offset:.3f}:d={safe_duration:.3f}[a0_fade];"
                    filter_complex += f"[1:a]adelay={int(offset*1000)}|{int(offset*1000)}[a1_delayed];"
                    filter_complex += f"[a1_delayed]afade=t=in:st={offset:.3f}:d={safe_duration:.3f}[a1_fade];"
                    filter_complex += f"[a0_fade][a1_fade]amix=inputs=2:duration=longest[outa]"
                map_args = ["-map", "[outv]", "-map", "[outa]"]
            elif has_audio_0:
                # 只有第一个视频有音频
                filter_complex = f"[0:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v0];[1:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v1];[v0][v1]xfade=transition='{transition_type}':duration={safe_duration:.3f}:offset={offset:.3f}[outv]"
                enable_loudnorm = getattr(self, 'enable_loudnorm', False)
                if enable_loudnorm:
                    # 启用响度统一：对音频进行响度统一
                    filter_complex += f";[0:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[outa]"
                    map_args = ["-map", "[outv]", "-map", "[outa]"]
                else:
                    map_args = ["-map", "[outv]", "-map", "0:a"]
            elif has_audio_1:
                # 只有第二个视频有音频
                filter_complex = f"[0:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v0];[1:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v1];[v0][v1]xfade=transition='{transition_type}':duration={safe_duration:.3f}:offset={offset:.3f}[outv]"
                enable_loudnorm = getattr(self, 'enable_loudnorm', False)
                if enable_loudnorm:
                    # 启用响度统一：对音频进行响度统一
                    filter_complex += f";[1:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[outa]"
                    map_args = ["-map", "[outv]", "-map", "[outa]"]
                else:
                    map_args = ["-map", "[outv]", "-map", "1:a"]
            else:
                # 两个视频都没有音频
                filter_complex = f"[0:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v0];[1:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v1];[v0][v1]xfade=transition='{transition_type}':duration={safe_duration:.3f}:offset={offset:.3f}[outv]"
                map_args = ["-map", "[outv]"]
        else:
            # 不使用转场效果，使用原来的concat方式
            self._log(f"🎬 转场未启用，使用concat方式合并")
            enable_loudnorm = getattr(self, 'enable_loudnorm', False)

            if has_audio_0 and has_audio_1:
                # 两个视频都有音频，合并视频和音频
                if enable_loudnorm:
                    # 启用响度统一：先对音频进行响度统一，再concat
                    filter_complex = "[0:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[a0_norm];[1:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[a1_norm];[0:v][a0_norm][1:v][a1_norm]concat=n=2:v=1:a=1[outv][outa]"
                else:
                    filter_complex = "[0:v][0:a][1:v][1:a]concat=n=2:v=1:a=1[outv][outa]"
                map_args = ["-map", "[outv]", "-map", "[outa]"]
            elif has_audio_0:
                # 只有第一个视频有音频
                if enable_loudnorm:
                    # 启用响度统一：对音频进行响度统一
                    filter_complex = "[0:v][1:v]concat=n=2:v=1:a=0[outv];[0:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[outa]"
                    map_args = ["-map", "[outv]", "-map", "[outa]"]
                else:
                    filter_complex = "[0:v][1:v]concat=n=2:v=1:a=0[outv]"
                    map_args = ["-map", "[outv]", "-map", "0:a"]
            elif has_audio_1:
                # 只有第二个视频有音频
                if enable_loudnorm:
                    # 启用响度统一：对音频进行响度统一
                    filter_complex = "[0:v][1:v]concat=n=2:v=1:a=0[outv];[1:a]loudnorm=I=-23:LRA=7:TP=-2:measured_I=-23:measured_LRA=7:measured_TP=-2:measured_thresh=-34:offset=0[outa]"
                    map_args = ["-map", "[outv]", "-map", "[outa]"]
                else:
                    filter_complex = "[0:v][1:v]concat=n=2:v=1:a=0[outv]"
                    map_args = ["-map", "[outv]", "-map", "1:a"]
            else:
                # 两个视频都没有音频
                filter_complex = "[0:v][1:v]concat=n=2:v=1:a=0[outv]"
                map_args = ["-map", "[outv]"]

        # 构建基础FFmpeg命令（包含码率控制参数）
        cmd_base = [
            str(self.ffmpeg_path),
            "-i", str(processed_main),
            "-i", str(processed_variant),
            "-filter_complex", filter_complex,
            *map_args,
            "-c:v", "placeholder",  # 将被GPU/CPU函数替换
            "-b:v", VIDEO_BITRATE,
            "-maxrate", "18000k",
            "-bufsize", "36000k",
            "-y",
            str(output_file)
        ]

        # 如果有音频流，添加音频编码器和音频码率
        if has_audio_0 or has_audio_1:
            cmd_base.insert(-2, "-c:a")
            cmd_base.insert(-2, "aac")
            cmd_base.insert(-2, "-b:a")
            cmd_base.insert(-2, "320k")

        # GPU优先编码策略
        success = False

        # 首先尝试GPU编码（如果启用）
        if GPU_ENABLED and is_gpu_supported():
            success = self._try_gpu_encode(cmd_base, output_file)

        # 如果GPU编码失败或未启用，回退到CPU编码
        if not success:
            success = self._fallback_cpu_encode(cmd_base, output_file)

        # 处理最终结果
        if success:
            self._log(f"✅ 合并成功: {output_file.name}")
            self._log(f"   前贴素材: {main_file.name}")
            self._log(f"   后贴素材: {variant_file.name}")
            # 清理临时预处理文件
            self._cleanup_temp_files(processed_main, processed_variant, main_file, variant_file)
        else:
            self._log(f"❌ 合并失败: {output_file.name}")
            self._log(f"   前贴素材: {main_file.name}")
            self._log(f"   后贴素材: {variant_file.name}")
            self._log(f"   错误信息: GPU和CPU编码都失败")
            # 即使失败也清理临时文件
            self._cleanup_temp_files(processed_main, processed_variant, main_file, variant_file)

    def _parse_transition_type(self, transition_text):
        """解析转场类型文本，返回FFmpeg转场效果名称"""
        # 完整的转场映射表
        transition_map = {
            "【【全随机】】": "random_all",
            "【【柔 · 随机】】": "random_soft",
            "【【硬 · 随机】】": "random_hard",
            "【柔】【叠化】fade": "fade",
            "【向左擦除】wipeleft": "wipeleft",
            "【向右擦除】wiperight": "wiperight",
            "【向上擦除】wipeup": "wipeup",
            "【向下擦除】wipedown": "wipedown",
            "【柔】【向左擦除】smoothleft": "smoothleft",
            "【柔】【向右擦除】smoothright": "smoothright",
            "【柔】【向上擦除】smoothup": "smoothup",
            "【柔】【向下擦除】smoothdown": "smoothdown",
            "【向左滑动】slideleft": "slideleft",
            "【向右滑动】slideright": "slideright",
            "【向上滑动】slideup": "slideup",
            "【向下滑动】slidedown": "slidedown",
            "【柔】【圆形展开】circleopen": "circleopen",
            "【柔】【圆形闭合】circleclose": "circleclose",
            "【柔】【垂直展开】vertopen": "vertopen",
            "【柔】【垂直闭合】vertclose": "vertclose",
            "【柔】【水平展开】horzopen": "horzopen",
            "【柔】【水平闭合】horzclose": "horzclose",
            "【柔】【景深转场】distance": "distance",
            "【时钟擦除】radial": "radial",
            "【像素模糊】pixelize": "pixelize",
            "【放大转场】zoomin": "zoomin",
            "【柔】【向左上擦除】diagtl": "diagtl",
            "【柔】【向右上擦除】diagtr": "diagtr",
            "【柔】【向左下擦除】diagbl": "diagbl",
            "【柔】【向右下擦除】diagbr": "diagbr",
            "【向左上擦除】wipetl": "wipetl",
            "【向右上擦除】wipetr": "wipetr",
            "【向左下擦除】wipebl": "wipebl",
            "【向右下擦除】wipebr": "wipebr",
            "【向左百叶窗】hlslice": "hlslice",
            "【向右百叶窗】hrslice": "hrslice",
            "【向上百叶窗】vuslice": "vuslice",
            "【向下百叶窗】vdslice": "vdslice",
            "【向左滑刺】hlwind": "hlwind",
            "【向右滑刺】hrwind": "hrwind",
            "【向上滑刺】vuwind": "vuwind",
            "【向下滑刺】vdwind": "vdwind"
        }
        return transition_map.get(transition_text, "fade")

    def _has_audio_stream(self, file_path: Path) -> bool:
        """检查视频文件是否有音频流"""
        cmd = [
            str(self.ffprobe_path),
            "-v", "error",
            "-select_streams", "a",
            "-show_entries", "stream=codec_type",
            "-of", "default=noprint_wrappers=1:nokey=1",
            str(file_path)
        ]

        try:
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                timeout=10,
                creationflags=CREATE_NO_WINDOW  #隐藏黑色弹窗
            )
            return "audio" in result.stdout
        except Exception:
            return False

    def stop(self):
        """停止混剪任务"""
        self._is_running = False
        self._log("混剪任务已停止")


class MixingThread(QThread):
    log_signal = Signal(str)
    progress_signal = Signal(int, int)

    def __init__(self, mixer):
        super().__init__()
        self.mixer = mixer

    def run(self):
        self.mixer.set_log_callback(self._log)
        self.mixer._is_running = True

        if not self.mixer.selected_strategy:
            self._log("❌ 未选择混剪策略")
            return

        # 计算总任务数
        total = 0
        if self.mixer.selected_strategy == "一个前贴只用一次":
            total = min(len(self.mixer.main_files), len(self.mixer.variant_files))
        elif self.mixer.selected_strategy == "一个前贴用两次后贴":
            total = len(self.mixer.main_files) * 2
        elif self.mixer.selected_strategy == "全笛卡尔积":
            total = len(self.mixer.main_files) * len(self.mixer.variant_files)
        elif self.mixer.selected_strategy == "一对一":
            main_groups = self.mixer._split_into_groups(self.mixer.main_files, self.mixer.k)
            variant_groups = self.mixer._split_into_groups(self.mixer.variant_files, self.mixer.k)
            total = sum(len(mg) * len(vg) for mg, vg in zip(main_groups, variant_groups))
        elif "一对" in self.mixer.selected_strategy:
            match_count = int(re.search(r'一对(\d+)组', self.mixer.selected_strategy).group(1))
            main_groups = self.mixer._split_into_groups(self.mixer.main_files, self.mixer.k)
            variant_groups = self.mixer._split_into_groups(self.mixer.variant_files, self.mixer.k)
            for i in range(self.mixer.k):
                v_indices = [(i + j) % self.mixer.k for j in range(match_count)]
                v_total = sum(len(variant_groups[j]) for j in v_indices)
                total += len(main_groups[i]) * v_total

        self._log(f"\n开始混剪任务: {self.mixer.selected_strategy}")
        self._log(f"预计生成 {total} 个视频")

        # 确保输出目录存在
        self.mixer.output_dir.mkdir(parents=True, exist_ok=True)

        # 根据选择的策略执行混剪
        if self.mixer.selected_strategy == "一个前贴只用一次":
            self._merge_true_one_to_one(total)
        elif self.mixer.selected_strategy == "一个前贴用两次后贴":
            self._merge_one_to_two(total)
        elif self.mixer.selected_strategy == "全笛卡尔积":
            self._merge_cartesian_product(total)
        elif self.mixer.selected_strategy == "一对一":
            self._merge_one_to_one(total)
        elif "一对" in self.mixer.selected_strategy:
            match_count = int(re.search(r'一对(\d+)组', self.mixer.selected_strategy).group(1))
            self._merge_one_to_many(match_count, total)

        self.mixer._is_running = False
        self._log("混剪任务完成!")

    def _log(self, message: str):
        self.log_signal.emit(message)

    def _merge_true_one_to_one(self, total):
        """执行一个前贴只用一次混剪"""
        main_count = len(self.mixer.main_files)
        variant_count = len(self.mixer.variant_files)

        # 如果后贴数量大于等于前贴数量，确保后贴选择不重复
        if variant_count >= main_count:
            # 随机选择不重复的后贴
            selected_variants = random.sample(self.mixer.variant_files, main_count)
            variant_files_to_use = selected_variants
        else:
            # 后贴数量小于前贴数量，可以重复选择后贴
            variant_files_to_use = []
            for i in range(main_count):
                variant_files_to_use.append(random.choice(self.mixer.variant_files))

        count = 0
        for i, main_file in enumerate(self.mixer.main_files):
            if not self.mixer._is_running:
                break

            if i >= len(variant_files_to_use):
                break

            variant_file = variant_files_to_use[i]
            output_file = self.mixer.output_dir / f"one_front_once_{i + 1}.mp4"
            self.mixer._merge_two_videos(main_file, variant_file, output_file, self.mixer.transition_config)
            count += 1
            self._log(f"📊 进度: {count}/{total}")
            self.progress_signal.emit(count, total)

    def _merge_one_to_two(self, total):
        """执行一个前贴用两次后贴混剪"""
        main_count = len(self.mixer.main_files)
        variant_count = len(self.mixer.variant_files)

        # 为每个前贴选择两个后贴
        variant_pairs = []

        if variant_count >= main_count * 2:
            # 后贴数量充足，确保不重复选择
            selected_variants = random.sample(self.mixer.variant_files, main_count * 2)
            for i in range(main_count):
                variant_pairs.append([selected_variants[i * 2], selected_variants[i * 2 + 1]])
        else:
            # 后贴数量不足，采用平均分配策略
            # 创建使用频次计数器
            usage_count = {variant: 0 for variant in self.mixer.variant_files}

            for i in range(main_count):
                # 为当前前贴选择两个不同的后贴
                pair = []

                # 选择第一个后贴（使用频次最少的）
                min_usage = min(usage_count.values())
                candidates = [v for v, count in usage_count.items() if count == min_usage]
                first_variant = random.choice(candidates)
                pair.append(first_variant)
                usage_count[first_variant] += 1

                # 选择第二个后贴（使用频次最少且不同于第一个的）
                if len(self.mixer.variant_files) > 1:
                    min_usage = min(usage_count.values())
                    candidates = [v for v, count in usage_count.items()
                                if count == min_usage and v != first_variant]
                    if not candidates:
                        # 如果没有不同的候选者，从所有不同于第一个的后贴中选择使用频次最少的
                        min_usage = min(count for v, count in usage_count.items() if v != first_variant)
                        candidates = [v for v, count in usage_count.items()
                                    if count == min_usage and v != first_variant]
                    second_variant = random.choice(candidates)
                    pair.append(second_variant)
                    usage_count[second_variant] += 1
                else:
                    # 只有一个后贴的情况，重复使用
                    pair.append(first_variant)
                    usage_count[first_variant] += 1

                variant_pairs.append(pair)

        # 执行混剪
        count = 0
        for i, main_file in enumerate(self.mixer.main_files):
            if not self.mixer._is_running:
                break

            variant_pair = variant_pairs[i]

            # 生成第一个视频
            output_file1 = self.mixer.output_dir / f"one_to_two_{i + 1}_1.mp4"
            self.mixer._merge_two_videos(main_file, variant_pair[0], output_file1, self.mixer.transition_config)
            count += 1
            self._log(f"📊 进度: {count}/{total}")
            self.progress_signal.emit(count, total)

            if not self.mixer._is_running:
                break

            # 生成第二个视频
            output_file2 = self.mixer.output_dir / f"one_to_two_{i + 1}_2.mp4"
            self.mixer._merge_two_videos(main_file, variant_pair[1], output_file2, self.mixer.transition_config)
            count += 1
            self._log(f"📊 进度: {count}/{total}")
            self.progress_signal.emit(count, total)

    def _merge_cartesian_product(self, total):
        count = 0
        for i, main_file in enumerate(self.mixer.main_files):
            if not self.mixer._is_running:
                break
            for j, variant_file in enumerate(self.mixer.variant_files):
                if not self.mixer._is_running:
                    break
                output_file = self.mixer.output_dir / f"merged_{i + 1}_{j + 1}.mp4"
                self.mixer._merge_two_videos(main_file, variant_file, output_file, self.mixer.transition_config)
                count += 1
                self._log(f"📊 进度: {count}/{total}")
                self.progress_signal.emit(count, total)

    def _merge_one_to_one(self, total):
        main_groups = self.mixer._split_into_groups(self.mixer.main_files, self.mixer.k)
        variant_groups = self.mixer._split_into_groups(self.mixer.variant_files, self.mixer.k)
        count = 0
        for group_idx, (main_group, variant_group) in enumerate(zip(main_groups, variant_groups)):
            if not self.mixer._is_running:
                break
            for i, main_file in enumerate(main_group):
                if not self.mixer._is_running:
                    break
                for j, variant_file in enumerate(variant_group):
                    if not self.mixer._is_running:
                        break
                    output_file = self.mixer.output_dir / f"merged_{group_idx + 1}_{i + 1}_{j + 1}.mp4"
                    self.mixer._merge_two_videos(main_file, variant_file, output_file, self.mixer.transition_config)
                    count += 1
                    self._log(f"📊 进度: {count}/{total}")
                    self.progress_signal.emit(count, total)

    def _merge_one_to_many(self, match_count, total):
        main_groups = self.mixer._split_into_groups(self.mixer.main_files, self.mixer.k)
        variant_groups = self.mixer._split_into_groups(self.mixer.variant_files, self.mixer.k)
        count = 0
        for i in range(self.mixer.k):
            if not self.mixer._is_running:
                break
            v_indices = [(i + j) % self.mixer.k for j in range(match_count)]
            for v_idx in v_indices:
                if not self.mixer._is_running:
                    break
                for main_file in main_groups[i]:
                    if not self.mixer._is_running:
                        break
                    for variant_file in variant_groups[v_idx]:
                        if not self.mixer._is_running:
                            break
                        output_file = self.mixer.output_dir / f"one_to_{match_count}_{i + 1}_{v_idx + 1}_{count + 1}.mp4"
                        self.mixer._merge_two_videos(main_file, variant_file, output_file, self.mixer.transition_config)
                        count += 1
                        self._log(f"📊 进度: {count}/{total}")
                        self.progress_signal.emit(count, total)


