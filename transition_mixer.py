#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
转场混剪工具类 - 复用tab_QT的转场功能到tab_HJ
"""

import subprocess
import random
from pathlib import Path
from typing import List, Optional

# 导入全局设置
try:
    from main import VIDEO_BITRATE, GPU_ENABLED, CPU_PRESET, GPU_PRESET, is_gpu_supported
except ImportError:
    # 如果无法导入，使用默认值
    VIDEO_BITRATE = "15000k"
    GPU_ENABLED = True
    CPU_PRESET = "slow"
    GPU_PRESET = "p2"
    
    def is_gpu_supported():
        return True

# Windows下隐藏控制台窗口的标志
import sys
if sys.platform.startswith('win'):
    CREATE_NO_WINDOW = 0x08000000
else:
    CREATE_NO_WINDOW = 0


class TransitionMixer:
    """转场混剪工具类，复用tab_QT的稳定转场实现"""
    
    def __init__(self, ffmpeg_path, ffprobe_path, log_callback=None):
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path
        self.log_callback = log_callback
        self.enable_loudnorm = False
    
    def _log(self, message: str):
        """日志输出"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)
    
    def set_loudnorm_enabled(self, enabled):
        """设置是否启用响度统一"""
        self.enable_loudnorm = enabled
        if enabled:
            self._log("🔊 已启用响度统一功能")
        else:
            self._log("🔇 已禁用响度统一功能")
    
    def _has_audio_stream(self, file_path: Path) -> bool:
        """检查视频文件是否有音频流"""
        cmd = [
            str(self.ffprobe_path),
            "-v", "error",
            "-select_streams", "a",
            "-show_entries", "stream=codec_type",
            "-of", "default=noprint_wrappers=1:nokey=1",
            str(file_path)
        ]

        try:
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                timeout=10,
                creationflags=CREATE_NO_WINDOW
            )
            return "audio" in result.stdout
        except Exception:
            return False
    
    def _parse_transition_type(self, transition_text):
        """解析转场类型文本，返回FFmpeg转场效果名称"""
        # 完整的转场映射表
        transition_map = {
            "【【全随机】】": "random_all",
            "【【柔 · 随机】】": "random_soft", 
            "【【硬 · 随机】】": "random_hard",
            "【柔】【叠化】fade": "fade",
            "【向左擦除】wipeleft": "wipeleft",
            "【向右擦除】wiperight": "wiperight",
            "【向上擦除】wipeup": "wipeup",
            "【向下擦除】wipedown": "wipedown",
            "【柔】【向左擦除】smoothleft": "smoothleft",
            "【柔】【向右擦除】smoothright": "smoothright",
            "【柔】【向上擦除】smoothup": "smoothup",
            "【柔】【向下擦除】smoothdown": "smoothdown",
            "【向左滑动】slideleft": "slideleft",
            "【向右滑动】slideright": "slideright",
            "【向上滑动】slideup": "slideup",
            "【向下滑动】slidedown": "slidedown",
            "【柔】【圆形展开】circleopen": "circleopen",
            "【柔】【圆形闭合】circleclose": "circleclose",
            "【柔】【垂直展开】vertopen": "vertopen",
            "【柔】【垂直闭合】vertclose": "vertclose",
            "【柔】【水平展开】horzopen": "horzopen",
            "【柔】【水平闭合】horzclose": "horzclose",
            "【柔】【景深转场】distance": "distance",
            "【时钟擦除】radial": "radial",
            "【像素模糊】pixelize": "pixelize",
            "【放大转场】zoomin": "zoomin",
            "【柔】【向左上擦除】diagtl": "diagtl",
            "【柔】【向右上擦除】diagtr": "diagtr",
            "【柔】【向左下擦除】diagbl": "diagbl",
            "【柔】【向右下擦除】diagbr": "diagbr",
            "【向左上擦除】wipetl": "wipetl",
            "【向右上擦除】wipetr": "wipetr",
            "【向左下擦除】wipebl": "wipebl",
            "【向右下擦除】wipebr": "wipebr",
            "【向左百叶窗】hlslice": "hlslice",
            "【向右百叶窗】hrslice": "hrslice",
            "【向上百叶窗】vuslice": "vuslice",
            "【向下百叶窗】vdslice": "vdslice",
            "【向左滑刺】hlwind": "hlwind",
            "【向右滑刺】hrwind": "hrwind",
            "【向上滑刺】vuwind": "vuwind",
            "【向下滑刺】vdwind": "vdwind"
        }
        return transition_map.get(transition_text, "fade")
    
    def _get_random_transition(self, transition_type):
        """获取随机转场效果 - 按用户要求的完整列表"""
        if transition_type == "【【全随机】】":
            # 从所有转场中随机选择 - 包含用户要求的42个特效
            all_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                             "circleopen", "circleclose", "vertopen", "vertclose", "horzopen", "horzclose",
                             "distance", "diagtl", "diagtr", "diagbl", "diagbr", "wipeleft", "wiperight",
                             "wipeup", "wipedown", "slideleft", "slideright", "slideup", "slidedown",
                             "radial", "pixelize", "zoomin", "wipetl", "wipetr", "wipebl", "wipebr",
                             "hlslice", "hrslice", "vuslice", "vdslice", "hlwind", "hrwind", "vuwind", "vdwind"]
            return random.choice(all_transitions)
        elif transition_type == "【【柔 · 随机】】":
            # 从柔和转场中随机选择 - 16个柔和特效
            soft_transitions = ["fade", "smoothleft", "smoothright", "smoothup", "smoothdown",
                              "circleopen", "circleclose", "vertopen", "vertclose",
                              "horzopen", "horzclose", "distance", "diagtl", "diagtr", "diagbl", "diagbr"]
            return random.choice(soft_transitions)
        elif transition_type == "【【硬 · 随机】】":
            # 从硬切转场中随机选择 - 20个硬切特效
            hard_transitions = ["wipeleft", "wiperight", "wipeup", "wipedown", "slideleft", "slideright",
                              "slideup", "slidedown", "radial", "pixelize", "zoomin", "wipetl", "wipetr",
                              "wipebl", "wipebr", "hlslice", "hrslice", "vuslice", "vdslice",
                              "hlwind", "hrwind", "vuwind", "vdwind"]
            return random.choice(hard_transitions)
        else:
            # 非随机转场，直接解析
            return self._parse_transition_type(transition_type)
    
    def _try_gpu_encode(self, cmd_base, output_file):
        """尝试GPU编码，失败时返回False"""
        try:
            # 导入主程序的GPU函数
            from main import build_gpu_ffmpeg_cmd

            # 使用统一的GPU加速函数
            gpu_cmd, using_gpu = build_gpu_ffmpeg_cmd(cmd_base, force_gpu=True)

            if not using_gpu:
                self._log("❌ GPU不可用，跳过GPU编码")
                return False

            self._log(f"🚀 GPU编码中...")

            result = subprocess.run(
                gpu_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                timeout=3600,
                creationflags=CREATE_NO_WINDOW
            )

            if result.returncode == 0 and output_file.exists() and output_file.stat().st_size > 0:
                self._log(f"✅ GPU加速编码成功")
                return True
            else:
                # 输出完整的错误信息用于调试
                error_msg = result.stderr if result.stderr else result.stdout if result.stdout else '未知错误'
                self._log(f"❌ GPU编码失败 (返回码: {result.returncode})")

                # 特殊处理Windows内存访问错误
                if result.returncode == 4294967274:
                    self._log("⚠️ 检测到FFmpeg内存访问错误，可能是滤镜过于复杂")
                else:
                    self._log(f"错误详情: {error_msg[:500]}")

                # 删除可能生成的不完整文件
                if output_file.exists():
                    output_file.unlink()
                return False

        except Exception as e:
            self._log(f"❌ GPU编码异常: {str(e)[:100]}")
            # 删除可能生成的不完整文件
            if output_file.exists():
                output_file.unlink()
            return False
    
    def _fallback_cpu_encode(self, cmd_base, output_file):
        """CPU编码回退方案"""
        try:
            # 构建CPU编码命令
            cpu_cmd = cmd_base.copy()

            # 找到视频编码器位置并替换为CPU编码器
            if "-c:v" in cpu_cmd:
                codec_index = cpu_cmd.index("-c:v") + 1
                cpu_cmd[codec_index] = "libx264"
            else:
                cpu_cmd.extend(["-c:v", "libx264"])

            # 添加CPU相关参数（不使用CRF，保持码率控制）
            cpu_cmd.extend(["-preset", CPU_PRESET])

            self._log(f"🔄 CPU编码中...")

            result = subprocess.run(
                cpu_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                timeout=3600,
                creationflags=CREATE_NO_WINDOW
            )

            if result.returncode == 0 and output_file.exists() and output_file.stat().st_size > 0:
                self._log(f"✅ CPU编码成功")
                return True
            else:
                # 输出完整的错误信息用于调试
                error_msg = result.stderr if result.stderr else result.stdout if result.stdout else '未知错误'
                self._log(f"❌ CPU编码也失败 (返回码: {result.returncode})")

                # 特殊处理Windows内存访问错误
                if result.returncode == 4294967274:
                    self._log("⚠️ 检测到FFmpeg内存访问错误，可能是滤镜过于复杂")
                else:
                    self._log(f"错误详情: {error_msg[:500]}")
                return False

        except Exception as e:
            self._log(f"❌ CPU编码异常: {str(e)[:100]}")
            return False



    def merge_two_videos_with_transition(self, video1: Path, video2: Path, output_path: Path,
                                       transition_type: str, transition_duration: float, mute_original: bool = False):
        """合并两个视频，使用tab_QT的稳定转场方式"""
        try:
            # 首先尝试转场合并
            if self._try_transition_merge(video1, video2, output_path, transition_type, transition_duration, mute_original):
                return True

            # 如果转场失败，尝试简化的转场（不使用响度统一）
            self._log("🔄 复杂转场失败，尝试简化转场...")
            original_loudnorm = self.enable_loudnorm
            self.enable_loudnorm = False

            if self._try_transition_merge(video1, video2, output_path, transition_type, transition_duration, mute_original):
                self.enable_loudnorm = original_loudnorm
                return True

            # 恢复原设置
            self.enable_loudnorm = original_loudnorm

            # 如果简化转场也失败，使用简单拼接
            self._log("🔄 简化转场也失败，尝试简单拼接...")
            return self._fallback_simple_concat(video1, video2, output_path, mute_original)

        except Exception as e:
            self._log(f"❌ 转场合并异常：{str(e)[:100]}")
            # 最后的回退：简单拼接
            try:
                return self._fallback_simple_concat(video1, video2, output_path, mute_original)
            except:
                return False

    def _try_transition_merge(self, video1: Path, video2: Path, output_path: Path,
                            transition_type: str, transition_duration: float, mute_original: bool = False):
        """尝试转场合并"""
        try:
            # 检查音频流
            has_audio_0 = self._has_audio_stream(video1)
            has_audio_1 = self._has_audio_stream(video2)

            # 处理随机转场
            if transition_type.startswith("【【") and "随机" in transition_type:
                actual_transition = self._get_random_transition(transition_type)
                self._log(f"🎲 随机转场: {transition_type} → {actual_transition}")
                transition_type = actual_transition
            elif transition_type.startswith("【"):
                # 非随机的中文转场，需要解析
                transition_type = self._parse_transition_type(transition_type)

            # 获取第一个视频的分辨率作为目标分辨率
            try:
                from utils import get_video_metadata, get_video_duration
                first_video_meta = get_video_metadata(video1)
                if first_video_meta and 'streams' in first_video_meta:
                    video_stream = first_video_meta['streams'][0]
                    target_width = video_stream.get('width', 1920)
                    target_height = video_stream.get('height', 1080)
                else:
                    target_width, target_height = 1920, 1080

                # 获取视频时长来计算正确的offset
                main_duration = get_video_duration(video1)
                variant_duration = get_video_duration(video2)

                if main_duration > 0 and variant_duration > 0:
                    # 更严格的转场时长验证：确保转场时长不会超过前后视频的最短时长的30%
                    max_safe_duration = min(main_duration * 0.3, variant_duration * 0.3)
                    safe_duration = min(transition_duration, max_safe_duration)
                    # 确保转场时长至少为0.1秒，最多为5秒
                    safe_duration = max(0.1, min(safe_duration, 5.0))
                    # 设置转场开始位置为前一个视频的末尾减去转场时长
                    offset = max(0, main_duration - safe_duration)
                    self._log(f"🎬 视频时长: 前贴={main_duration:.2f}s, 后贴={variant_duration:.2f}s")
                    self._log(f"🎬 转场计算: 安全时长={safe_duration:.2f}s, offset={offset:.2f}s")
                else:
                    # 使用更保守的默认值
                    safe_duration = min(transition_duration, 2.0)
                    offset = 0
                    self._log(f"⚠️ 无法获取视频时长，使用保守默认转场参数: {safe_duration:.2f}s")
            except:
                target_width, target_height = 1920, 1080
                safe_duration = transition_duration
                offset = 0
                self._log(f"⚠️ 获取视频信息失败，使用默认参数")

            # 使用转场效果的滤镜 - 复用tab_QT的稳定实现
            if has_audio_0 and has_audio_1:
                # 两个视频都有音频，实现音频转场：A音频淡出 + B音频淡入
                filter_complex = f"[0:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v0];"
                filter_complex += f"[1:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v1];"
                filter_complex += f"[v0][v1]xfade=transition='{transition_type}':duration={safe_duration:.3f}:offset={offset:.3f}[outv];"

                # 音频转场：进一步简化，减少滤镜复杂度
                if self.enable_loudnorm:
                    # 启用响度统一：使用最简化的响度统一参数，避免复杂滤镜链
                    filter_complex += f"[0:a]loudnorm=I=-16:LRA=11:TP=-1.5[a0_norm];"
                    filter_complex += f"[1:a]loudnorm=I=-16:LRA=11:TP=-1.5[a1_norm];"
                    filter_complex += f"[a0_norm]afade=t=out:st={offset:.3f}:d={safe_duration:.3f}[a0_fade];"
                    # 修复音频延迟计算：确保延迟时间正确，避免音频同步问题
                    delay_ms = max(0, int(offset * 1000))
                    filter_complex += f"[a1_norm]adelay={delay_ms}|{delay_ms}[a1_delayed];"
                    filter_complex += f"[a1_delayed]afade=t=in:st={offset:.3f}:d={safe_duration:.3f}[a1_fade];"
                    filter_complex += f"[a0_fade][a1_fade]amix=inputs=2:duration=longest:normalize=0[outa]"
                else:
                    # 不启用响度统一：使用最简单的音频转场
                    filter_complex += f"[0:a]afade=t=out:st={offset:.3f}:d={safe_duration:.3f}[a0_fade];"
                    # 修复音频延迟计算：确保延迟时间正确，避免音频同步问题
                    delay_ms = max(0, int(offset * 1000))
                    filter_complex += f"[1:a]adelay={delay_ms}|{delay_ms}[a1_delayed];"
                    filter_complex += f"[a1_delayed]afade=t=in:st={offset:.3f}:d={safe_duration:.3f}[a1_fade];"
                    filter_complex += f"[a0_fade][a1_fade]amix=inputs=2:duration=longest:normalize=0[outa]"
                map_args = ["-map", "[outv]", "-map", "[outa]"]
            elif has_audio_0:
                # 只有第一个视频有音频
                filter_complex = f"[0:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v0];[1:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v1];[v0][v1]xfade=transition='{transition_type}':duration={safe_duration:.3f}:offset={offset:.3f}[outv]"
                if self.enable_loudnorm:
                    # 启用响度统一：使用简化参数
                    filter_complex += f";[0:a]loudnorm[outa]"
                    map_args = ["-map", "[outv]", "-map", "[outa]"]
                else:
                    map_args = ["-map", "[outv]", "-map", "0:a"]
            elif has_audio_1:
                # 只有第二个视频有音频
                filter_complex = f"[0:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v0];[1:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v1];[v0][v1]xfade=transition='{transition_type}':duration={safe_duration:.3f}:offset={offset:.3f}[outv]"
                if self.enable_loudnorm:
                    # 启用响度统一：使用简化参数
                    filter_complex += f";[1:a]loudnorm[outa]"
                    map_args = ["-map", "[outv]", "-map", "[outa]"]
                else:
                    map_args = ["-map", "[outv]", "-map", "1:a"]
            else:
                # 两个视频都没有音频
                filter_complex = f"[0:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v0];[1:v]scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2[v1];[v0][v1]xfade=transition='{transition_type}':duration={safe_duration:.3f}:offset={offset:.3f}[outv]"
                map_args = ["-map", "[outv]"]

            # 如果需要静音
            if mute_original:
                map_args = ["-map", "[outv]"]

            # 构建基础FFmpeg命令（包含码率控制参数）
            cmd_base = [
                str(self.ffmpeg_path),
                "-i", str(video1),
                "-i", str(video2),
                "-filter_complex", filter_complex,
                *map_args,
                "-c:v", "placeholder",  # 将被GPU/CPU函数替换
                "-b:v", VIDEO_BITRATE,
                "-maxrate", "18000k",
                "-bufsize", "36000k",
                "-y",
                str(output_path)
            ]

            # 如果有音频流且不静音，添加音频编码器和音频码率
            if (has_audio_0 or has_audio_1) and not mute_original:
                cmd_base.insert(-2, "-c:a")
                cmd_base.insert(-2, "aac")
                cmd_base.insert(-2, "-b:a")
                cmd_base.insert(-2, "256k")  # 提高音频码率，减少失真
                cmd_base.insert(-2, "-ar")
                cmd_base.insert(-2, "48000")  # 使用48kHz采样率，提高音质

            # 输出完整的FFmpeg命令用于调试
            self._log(f"🔧 FFmpeg命令预览: {' '.join(cmd_base[:10])}...{cmd_base[-3:]}")
            self._log(f"🎬 滤镜复杂度: {len(filter_complex)} 字符")

            # 检查滤镜复杂度，如果过于复杂则直接使用简单拼接
            if len(filter_complex) > 600:
                self._log(f"⚠️ 滤镜过于复杂 ({len(filter_complex)} 字符)，直接使用简单拼接")
                return self._fallback_simple_concat(video1, video2, output_path, mute_original)

            # GPU优先编码策略
            success = False

            # 首先尝试GPU编码（如果启用）
            if GPU_ENABLED and is_gpu_supported():
                success = self._try_gpu_encode(cmd_base, output_path)

            # 如果GPU编码失败或未启用，回退到CPU编码
            if not success:
                success = self._fallback_cpu_encode(cmd_base, output_path)

            return success

        except Exception as e:
            self._log(f"❌ 转场合并异常：{str(e)[:100]}")
            # 异常时自动回退到简单拼接
            self._log("🔄 转场失败，自动回退到简单拼接...")
            try:
                return self._fallback_simple_concat(video1, video2, output_path, mute_original)
            except Exception as fallback_e:
                self._log(f"❌ 简单拼接也失败：{str(fallback_e)[:100]}")
                return False

    def _fallback_simple_concat(self, video1: Path, video2: Path, output_path: Path, mute_original: bool = False):
        """简单拼接回退方案"""
        try:
            self._log("🔄 使用简单拼接方式...")

            # 创建临时concat文件
            concat_file = output_path.parent / "temp_concat.txt"
            with open(concat_file, 'w', encoding='utf-8') as f:
                # 使用绝对路径并确保路径格式正确，避免特殊字符问题
                video1_abs = str(video1.absolute()).replace('\\', '/').replace("'", "\\'")
                video2_abs = str(video2.absolute()).replace('\\', '/').replace("'", "\\'")
                f.write(f"file '{video1_abs}'\n")
                f.write(f"file '{video2_abs}'\n")

            # 构建简单的concat命令
            cmd = [
                str(self.ffmpeg_path),
                "-f", "concat",
                "-safe", "0",
                "-i", str(concat_file),
                "-c", "copy",
                "-y",
                str(output_path)
            ]

            # 如果需要静音
            if mute_original:
                cmd = [
                    str(self.ffmpeg_path),
                    "-f", "concat",
                    "-safe", "0",
                    "-i", str(concat_file),
                    "-c:v", "copy",
                    "-an",  # 移除音频
                    "-y",
                    str(output_path)
                ]

            self._log(f"🔧 简单拼接命令: {' '.join(cmd[:8])}...")

            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                timeout=300,  # 减少超时时间，避免长时间等待
                creationflags=CREATE_NO_WINDOW
            )

            # 清理临时文件
            try:
                concat_file.unlink()
            except:
                pass

            if result.returncode == 0 and output_path.exists() and output_path.stat().st_size > 0:
                self._log(f"✅ 简单拼接成功")
                return True
            else:
                error_msg = result.stderr if result.stderr else result.stdout if result.stdout else '未知错误'
                self._log(f"❌ 简单拼接也失败: {error_msg[:300]}")
                return False

        except Exception as e:
            self._log(f"❌ 简单拼接异常：{str(e)[:100]}")
            return False
