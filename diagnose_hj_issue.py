#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
tab_HJ问题诊断脚本 - 专门诊断用户遇到的合成失败问题
"""

import subprocess
import os
import tempfile
from pathlib import Path

# Windows下隐藏控制台窗口
CREATE_NO_WINDOW = 0x08000000 if os.name == 'nt' else 0

def check_ffmpeg_installation():
    """检查FFmpeg安装"""
    print("🔍 检查FFmpeg安装...")
    
    ffmpeg_paths = [
        Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
        Path("C:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
        Path("E:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe"),
        Path("F:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    ]
    
    for path in ffmpeg_paths:
        if path.exists():
            print(f"✅ 找到FFmpeg: {path}")
            return path
    
    print("❌ 未找到FFmpeg")
    return None

def create_minimal_test_video(output_path, duration=3):
    """创建最小测试视频"""
    ffmpeg_path = check_ffmpeg_installation()
    if not ffmpeg_path:
        return False
    
    cmd = [
        str(ffmpeg_path),
        "-f", "lavfi",
        "-i", f"color=red:duration={duration}:size=320x240:rate=30",
        "-f", "lavfi", 
        "-i", f"sine=frequency=440:duration={duration}",
        "-c:v", "libx264",
        "-c:a", "aac",
        "-t", str(duration),
        "-y",
        str(output_path)
    ]
    
    print(f"🎬 创建最小测试视频: {output_path}")
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        creationflags=CREATE_NO_WINDOW
    )
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"✅ 创建成功: {os.path.getsize(output_path)} bytes")
        return True
    else:
        print(f"❌ 创建失败: {result.stderr[:200]}")
        return False

def test_segment_extraction(input_video, output_segment, duration=2):
    """测试视频片段提取（模拟tab_HJ的第一步）"""
    print(f"\n🔍 测试片段提取...")
    
    ffmpeg_path = check_ffmpeg_installation()
    if not ffmpeg_path:
        return False
    
    cmd = [
        str(ffmpeg_path),
        "-i", str(input_video),
        "-t", str(duration),
        "-c", "copy",
        "-y",
        str(output_segment)
    ]
    
    print(f"🔧 提取命令: {' '.join(cmd)}")
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        creationflags=CREATE_NO_WINDOW
    )
    
    if result.returncode == 0 and os.path.exists(output_segment):
        print(f"✅ 片段提取成功: {os.path.getsize(output_segment)} bytes")
        return True
    else:
        print(f"❌ 片段提取失败 (返回码: {result.returncode})")
        print(f"错误详情: {result.stderr[:300]}")
        return False

def test_gpu_encoding_simple(video1, video2, output_path):
    """测试最简单的GPU编码"""
    print(f"\n🔍 测试简单GPU编码...")
    
    ffmpeg_path = check_ffmpeg_installation()
    if not ffmpeg_path:
        return False
    
    # 最简单的GPU编码命令
    cmd = [
        str(ffmpeg_path),
        "-hwaccel", "cuda",
        "-i", str(video1),
        "-i", str(video2),
        "-filter_complex", "[0:v][1:v]concat=n=2:v=1:a=0[outv]",
        "-map", "[outv]",
        "-c:v", "h264_nvenc",
        "-preset", "p2",
        "-b:v", "5000k",
        "-y",
        str(output_path)
    ]
    
    print(f"🔧 GPU命令: {' '.join(cmd[:10])}...")
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        timeout=60,
        creationflags=CREATE_NO_WINDOW
    )
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"✅ 简单GPU编码成功: {os.path.getsize(output_path)} bytes")
        return True
    else:
        print(f"❌ 简单GPU编码失败 (返回码: {result.returncode})")
        print(f"错误详情: {result.stderr[:500]}")
        return False

def test_cpu_encoding_simple(video1, video2, output_path):
    """测试最简单的CPU编码"""
    print(f"\n🔍 测试简单CPU编码...")
    
    ffmpeg_path = check_ffmpeg_installation()
    if not ffmpeg_path:
        return False
    
    # 最简单的CPU编码命令
    cmd = [
        str(ffmpeg_path),
        "-i", str(video1),
        "-i", str(video2),
        "-filter_complex", "[0:v][1:v]concat=n=2:v=1:a=0[outv]",
        "-map", "[outv]",
        "-c:v", "libx264",
        "-preset", "slow",
        "-b:v", "5000k",
        "-y",
        str(output_path)
    ]
    
    print(f"🔧 CPU命令: {' '.join(cmd[:10])}...")
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        encoding='utf-8',
        timeout=60,
        creationflags=CREATE_NO_WINDOW
    )
    
    if result.returncode == 0 and os.path.exists(output_path):
        print(f"✅ 简单CPU编码成功: {os.path.getsize(output_path)} bytes")
        return True
    else:
        print(f"❌ 简单CPU编码失败 (返回码: {result.returncode})")
        print(f"错误详情: {result.stderr[:500]}")
        return False

def test_permission_and_space():
    """测试文件权限和磁盘空间"""
    print(f"\n🔍 测试文件权限和磁盘空间...")
    
    try:
        # 测试当前目录的写权限
        test_file = Path("test_permission.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        
        if test_file.exists():
            test_file.unlink()
            print("✅ 当前目录写权限正常")
        else:
            print("❌ 当前目录写权限异常")
            return False
        
        # 检查磁盘空间
        import shutil
        total, used, free = shutil.disk_usage(".")
        free_gb = free // (1024**3)
        print(f"✅ 磁盘剩余空间: {free_gb} GB")
        
        if free_gb < 1:
            print("⚠️ 磁盘空间不足，可能影响视频处理")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 权限或空间检查失败: {str(e)}")
        return False

def main():
    """主诊断函数"""
    print("🎬 tab_HJ问题诊断")
    print("=" * 50)
    
    # 1. 检查基础环境
    if not check_ffmpeg_installation():
        print("❌ FFmpeg未安装或路径错误")
        return
    
    if not test_permission_and_space():
        print("❌ 文件权限或磁盘空间问题")
        return
    
    # 2. 创建测试环境
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试视频
        video1 = temp_path / "test1.mp4"
        video2 = temp_path / "test2.mp4"
        
        if not create_minimal_test_video(video1):
            print("❌ 无法创建测试视频1")
            return
        
        if not create_minimal_test_video(video2):
            print("❌ 无法创建测试视频2")
            return
        
        # 3. 测试片段提取
        segment1 = temp_path / "segment1.mp4"
        segment2 = temp_path / "segment2.mp4"
        
        extract1 = test_segment_extraction(video1, segment1)
        extract2 = test_segment_extraction(video2, segment2)
        
        if not extract1 or not extract2:
            print("❌ 片段提取失败，这可能是问题根源")
            return
        
        # 4. 测试编码
        output_gpu = temp_path / "output_gpu.mp4"
        output_cpu = temp_path / "output_cpu.mp4"
        
        gpu_success = test_gpu_encoding_simple(segment1, segment2, output_gpu)
        cpu_success = test_cpu_encoding_simple(segment1, segment2, output_cpu)
        
        # 5. 汇总结果
        print("\n" + "=" * 50)
        print("📊 诊断结果:")
        print(f"  FFmpeg安装: ✅ 正常")
        print(f"  文件权限: ✅ 正常")
        print(f"  片段提取: {'✅ 正常' if extract1 and extract2 else '❌ 异常'}")
        print(f"  GPU编码: {'✅ 正常' if gpu_success else '❌ 异常'}")
        print(f"  CPU编码: {'✅ 正常' if cpu_success else '❌ 异常'}")
        
        if not gpu_success and not cpu_success:
            print("\n❌ GPU和CPU编码都失败，可能是FFmpeg配置问题")
        elif not gpu_success:
            print("\n⚠️ GPU编码失败，但CPU编码正常，建议检查GPU驱动")
        elif extract1 and extract2 and gpu_success and cpu_success:
            print("\n✅ 基础功能正常，问题可能在tab_HJ的具体实现")
            print("建议检查：")
            print("  1. 实际素材文件的格式和编码")
            print("  2. 转场滤镜的复杂度")
            print("  3. 响度统一功能的参数")
            print("  4. 文件路径中的特殊字符")

if __name__ == "__main__":
    main()
