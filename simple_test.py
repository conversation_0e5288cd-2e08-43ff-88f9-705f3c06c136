#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试预览功能
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow
from PySide6.QtCore import QTimer

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import MainWindow

def test_preview():
    """测试预览功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 等待界面初始化
    def after_init():
        print("=== 开始测试预览功能 ===")
        
        # 检查预览相关属性
        print(f"有preview_slider: {hasattr(window, 'preview_slider')}")
        print(f"有current_preview_video: {hasattr(window, 'current_preview_video')}")
        
        # 模拟选择AI水印
        print("勾选AI标识复选框")
        window.ui.checkBox_AI.setChecked(True)
        
        # 检查水印状态
        has_watermarks = window._has_enabled_watermarks()
        print(f"检测到水印: {has_watermarks}")
        
        # 手动触发预览更新
        print("手动触发预览更新")
        window._on_watermark_setting_changed()
        
        # 等待一段时间后退出
        QTimer.singleShot(3000, app.quit)
    
    # 延迟执行测试
    QTimer.singleShot(1000, after_init)
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_preview()
